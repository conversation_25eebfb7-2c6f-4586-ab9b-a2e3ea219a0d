import { EAirdropConditionType } from '@/constants/airdrop';

export interface IAirdropHoldersParams {
  tokenAddress?: string;
  isTransferred?: boolean;
  limit: number;
  page: number;
}

export interface IAirdropHolder {
  holderAddress: string;
  holdingTokenAddress: string;
  createdAt: string;
  isTransferred: boolean;
  hash: string;
  number: number;
  balanceFormatted: {
    $numberDecimal: number;
  };
}

interface ITokenSolPrice {
  symbol: string;
  name: string;
  iconUri: string;
  tokenAddress: string;
  solPrice: number;
}
export interface IAirdropListSolPrice {
  data: ITokenSolPrice[];
}

export interface IAirdropEventHoldersParams {
  type: EAirdropConditionType;
  inputData: string[];
}

export interface IAirdropEventHolders {
  walletAddress: string;
  balance: number;
  createdAt: string;
  expiredAt: string;
  point: number;
  rank: string;
  updatedAt: string;
  volume24h: number;
}

export interface IAirdropEventConfig {
  eventName: string;
  tokenAirdrop: string;
  totalAirdropAmount: number;
  airdropStartAt: string;
  airdropCondition: {
    type: 'TOKEN_HOLDERS' | 'NFT_HOLDERS' | 'X_REPLY' | 'CSV';
    data: string[];
  };
  gasFeePaymentType: 'WALLET_AIRDROP';
  description: string;
  descriptionImages: string[];
  socials: {
    name: string;
    url: string;
  }[];
  selectedTempIds: {
    tempId: string;
  }[];
}
