import { CHAIN } from '@tonconnect/ui-react';
import { Address } from 'viem';

import { bscTestnet, mainnet, polygon, polygonAmoy, sepolia } from 'wagmi/chains';

export const isMainnet = process.env.NEXT_PUBLIC_APP_NETWORK === 'mainnet';
export const quickNodeRpc = {
  mainnet: process.env.NEXT_PUBLIC_QUICK_NODE_URL || '',
  devnet: process.env.NEXT_PUBLIC_QUICK_NODE_DEV_URL || '',
};

const polygonChainIcon = '/icons/chain-polygon.svg';
const ethChainIcon = '/icons/token-eth.svg';
// const bscChainIcon = '/icons/chain-bsc.svg';
// const spTokenIcon = '/icons/token-sp.svg'; // TODO
// const btcTokenIcon = '/icons/token-btc.svg';
const ethTokenIcon = '/icons/token-eth.svg';
const usdtTokenIcon = '/icons/token-usdt.svg';
// const usdcTokenIcon = '/icons/token-usdc.svg';
const solTokenIcon = '/icons/chain-sol.svg';
const tonTokenIcon = '/icons/token-ton.svg';

export type Token = {
  symbol: ETokenSymbol;
  name: string;
  icon: string;
  address: Address | null;
  chainId: EChainId;
  decimal: number;
};

export type Chain = {
  name: string;
  id: number;
  icon: string;
  blockExplorer: string;
};

export enum EChainId {
  Ethereum = mainnet.id,
  Polygon = polygon.id,
  Amoy = polygonAmoy.id,
  Sepolia = sepolia.id,
  BSCTestnet = bscTestnet.id,
  Solana = 900,
  SolanaDevnet = 901,
  Ton = Number(CHAIN.MAINNET),
  TonTestnet = Number(CHAIN.TESTNET),
}

export enum ETokenSymbol {
  // SP = 'SP',   // TODO
  WBTC = 'WBTC',
  WETH = 'WETH',
  USDT = 'USDT',
  USDC = 'USDC',
  MATIC = 'MATIC',
  SOL = 'SOL',
  TON = 'TON',
}

// @ts-ignore
const mainnetChains: Record<EChainId, Chain> = {
  [EChainId.Ethereum]: {
    name: 'Ethereum',
    id: EChainId.Ethereum,
    icon: ethChainIcon,
    blockExplorer: 'https://etherscan.io',
  },
  [EChainId.Polygon]: {
    name: 'Polygon',
    id: EChainId.Polygon,
    icon: polygonChainIcon,
    blockExplorer: 'https://polygonscan.com',
  },
  [EChainId.Solana]: {
    name: 'Solana',
    id: EChainId.Solana,
    icon: solTokenIcon,
    blockExplorer: 'https://solscan.io',
  },
  [EChainId.Ton]: {
    name: 'Ton',
    id: EChainId.Ton,
    icon: tonTokenIcon,
    blockExplorer: 'https://tonscan.org',
  },
  // [EChainId.BSCTestnet]: { //TODO
  //   name: 'Binance',
  //   id: EChainId.BSCTestnet,
  //   icon: polygonChainIcon,
  //   blockExplorer: 'https://polygonscan.com',
  // },
};

// @ts-ignore
const testnetChains: Record<EChainId, Chain> = {
  [EChainId.Amoy]: {
    name: 'Polygon',
    id: EChainId.Amoy,
    icon: polygonChainIcon,
    blockExplorer: 'https://amoy.polygonscan.com',
  },
  [EChainId.Sepolia]: {
    name: 'Ethereum',
    id: EChainId.Sepolia,
    icon: ethChainIcon,
    blockExplorer: 'https://sepolia.etherscan.io',
  },
  [EChainId.SolanaDevnet]: {
    name: 'Solana',
    id: EChainId.SolanaDevnet,
    icon: solTokenIcon,
    blockExplorer: 'https://solscan.io/?cluster=devnet',
  },
  [EChainId.TonTestnet]: {
    name: 'Ton',
    id: EChainId.TonTestnet,
    icon: tonTokenIcon,
    blockExplorer: 'https://tonscan.org',
  },
  // [EChainId.BSCTestnet]: {
  //   name: 'Binance',
  //   id: EChainId.BSCTestnet,
  //   icon: bscChainIcon,
  //   blockExplorer: 'https://bscscan.com/',
  // },
};

export const chainList: Record<EChainId, Chain> = isMainnet ? mainnetChains : testnetChains;

const tokenAddresses: Record<EChainId, Record<ETokenSymbol | string, Address | string | null>> = {
  [EChainId.Ethereum]: {
    // [ETokenSymbol.SP]: null, // TODO
    // [ETokenSymbol.WBTC]: '******************************************',
    [ETokenSymbol.WETH]: null /* Native token */,
    [ETokenSymbol.USDT]: '******************************************',
    // [ETokenSymbol.USDC]: '******************************************',
    [ETokenSymbol.MATIC]: '******************************************',
  },
  [EChainId.Polygon]: {
    // [ETokenSymbol.SP]: null, // TODO
    // [ETokenSymbol.WBTC]: '******************************************',
    [ETokenSymbol.WETH]: '******************************************',
    // [ETokenSymbol.USDT]: '******************************************',
    // [ETokenSymbol.USDC]: '******************************************',
    [ETokenSymbol.MATIC]: null /* Native token */,
  },
  [EChainId.Sepolia]: {
    // [ETokenSymbol.SP]: null, // TODO
    // [ETokenSymbol.WBTC]: '******************************************',
    [ETokenSymbol.WETH]: null /* Native token */,
    [ETokenSymbol.USDT]: '******************************************',
    // [ETokenSymbol.USDC]: '******************************************',
    [ETokenSymbol.MATIC]: '******************************************',
  },
  [EChainId.Amoy]: {
    // [ETokenSymbol.SP]: '******************************************', //TODO
    // [ETokenSymbol.WBTC]: '******************************************',
    [ETokenSymbol.WETH]: '******************************************',
    // [ETokenSymbol.USDT]: '******************************************',
    // [ETokenSymbol.USDC]: '******************************************',
    [ETokenSymbol.MATIC]: null /* Native token */,
  },
  [EChainId.Solana]: {
    [ETokenSymbol.SOL]: 'So11111111111111111111111111111111111111112',
  },
  [EChainId.SolanaDevnet]: {
    [ETokenSymbol.SOL]: 'So11111111111111111111111111111111111111112',
  },
  // [EChainId.BSCTestnet]: {
  //   [ETokenSymbol.SP]: '******************************************',
  //   [ETokenSymbol.WBTC]: '******************************************',
  //   [ETokenSymbol.WETH]: '******************************************',
  //   [ETokenSymbol.USDT]: '******************************************',
  //   [ETokenSymbol.USDC]: '******************************************',
  //   [ETokenSymbol.MATIC]: null /* Native token */,
  // },
};

export const baseTokens: Record<ETokenSymbol | string, Omit<Token, 'address' | 'chainId'>> = {
  // TODO
  // [ETokenSymbol.SP]: {
  //   symbol: ETokenSymbol.SP,
  //   name: 'Smart Pocket',
  //   icon: spTokenIcon,
  //   decimal: 18,
  // },
  // [ETokenSymbol.WBTC]: {
  //   symbol: ETokenSymbol.WBTC,
  //   name: 'Wrapped Bitcoin',
  //   icon: btcTokenIcon,
  //   decimal: 8,
  // },
  [ETokenSymbol.WETH]: {
    symbol: ETokenSymbol.WETH,
    name: 'WETH',
    icon: ethTokenIcon,
    decimal: 18,
  },
  [ETokenSymbol.USDT]: {
    symbol: ETokenSymbol.USDT,
    name: 'Tether',
    icon: usdtTokenIcon,
    decimal: 6,
  },
  // [ETokenSymbol.USDC]: {
  //   symbol: ETokenSymbol.USDC,
  //   name: 'USD Coin',
  //   icon: usdcTokenIcon,
  //   decimal: 6,
  // },
  [ETokenSymbol.MATIC]: {
    symbol: ETokenSymbol.MATIC,
    name: 'Matic',
    icon: polygonChainIcon,
    decimal: 18,
  },
  [ETokenSymbol.SOL]: {
    symbol: ETokenSymbol.SOL,
    name: 'Solana',
    icon: solTokenIcon,
    decimal: 9,
  },
  [ETokenSymbol.TON]: {
    symbol: ETokenSymbol.TON,
    name: 'Ton',
    icon: tonTokenIcon,
    decimal: 9,
  },
};

export const getChainTokenList = (chainId: EChainId): Token[] => {
  const baseToken = Object.values(baseTokens);
  const data: Token[] = [];
  baseToken.map((token) => {
    if (
      [EChainId.Amoy, EChainId.Polygon].includes(chainId) &&
      [ETokenSymbol.USDT, ETokenSymbol.WETH, ETokenSymbol.SOL, ETokenSymbol.TON].includes(
        token.symbol
      )
    ) {
      return null;
    }
    if (
      [EChainId.Ethereum, EChainId.Sepolia].includes(chainId) &&
      [ETokenSymbol.MATIC, ETokenSymbol.SOL, ETokenSymbol.TON].includes(token.symbol)
    ) {
      return null;
    }
    if (
      [EChainId.Solana, EChainId.SolanaDevnet].includes(chainId) &&
      [ETokenSymbol.WETH, ETokenSymbol.USDT, ETokenSymbol.MATIC, ETokenSymbol.TON].includes(
        token.symbol
      )
    ) {
      return null;
    }
    if (
      [EChainId.Ton, EChainId.TonTestnet].includes(chainId) &&
      [ETokenSymbol.WETH, ETokenSymbol.USDT, ETokenSymbol.MATIC, ETokenSymbol.SOL].includes(
        token.symbol
      )
    ) {
      return null;
    }
    return data.push({
      ...token,
      chainId,
      decimal: chainId === EChainId.Sepolia ? 6 : chainId === EChainId.Amoy ? 18 : token.decimal,
      address: tokenAddresses[chainId]?.[token.symbol] as Address,
    });
  });

  return data;
};

export const SOLANA_TOKEN_ADDRESS = 'So11111111111111111111111111111111111111112';
export const SOLANA_TOKEN_PROGRAM = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA';
