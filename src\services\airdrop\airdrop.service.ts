import { Api } from '@/lib/axios';
import { IListResponse } from '../verify-meme/verify-meme.types';
import {
  IAirdropEventConfig,
  IAirdropEventHolders,
  IAirdropEventHoldersParams,
  IAirdropHolder,
  IAirdropHoldersParams,
  IAirdropListSolPrice,
} from './airdrop.types';
import { API_URL, memeEndpoint } from '../api-url';

export const AirdropService = {
  getAirdropHolders: (params: IAirdropHoldersParams): Promise<IListResponse<IAirdropHolder>> => {
    return Api.get(memeEndpoint + API_URL.AIRDROP + '/get-top-holders', { params });
  },
  getTokenSolPrice: (tokenAddresses: string[]): Promise<IAirdropListSolPrice> => {
    return Api.post(API_URL.AIRDROP_LIST_SOL_PRICE, { tokenAddresses });
  },
  postAirdropEventHolders: (
    params: IAirdropEventHoldersParams
  ): Promise<IAirdropEventHolders[]> => {
    return Api.post(memeEndpoint + API_URL.AIRDROP_EVENT_HOLDERS, params);
  },
  postAirdropEventConfig: (payload: IAirdropEventConfig) => {
    return Api.post(memeEndpoint + API_URL.AIRDROP_EVENT_CONFIG, payload);
  },
};
