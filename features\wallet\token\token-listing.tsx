import { TokenItem } from '@/features/wallet/token/token-item';
import {
  chainList,
  EChainId,
  ETokenSymbol,
  getChainTokenList,
  isMainnet,
  SOLANA_TOKEN_ADDRESS,
  Token,
} from '@/lib/web3/constants';
// import ChainIcon from '@/public/icons/chain-all-icon.svg';
import { isValidSolanaAddress } from '@/utils/common';
import Image from 'next/image';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { initializeWhenDetected } from '@solflare-wallet/metamask-wallet-standard';
import { useAuth } from '@/hooks/use-auth';
import { Address } from 'viem';
import TonConnectBtn from './ton-connect-btn';
import useSolTokenBalance from '@/features/wallet/token/hooks/use-sol-token-balance';
import { PublicKey } from '@solana/web3.js';
import { useGetSolPriceAirdrop } from '@/services/airdrop';
import FetchDataStatementWrapper from '@/components/shared/FetchDataStatementWrapper';
import { useSolUsdPrice } from '@/services/meme-launchpad';
import { useRouter } from 'next/navigation';
import { routePaths } from '@/constants/common';
import BigNumber from 'bignumber.js';
import NumberDisplay from '@/components/shared/NumberDisplay';

initializeWhenDetected();
export const ALL_CHAIN_VALUE = 0;

export const chainListKeys = isMainnet ? [EChainId.Solana] : [EChainId.SolanaDevnet];

const customChainList = chainListKeys.map((chainId) => {
  const chain = chainList[chainId];
  return chain;
});

export const TokenListing = ({
  address,
  onClickItem,
  search,
  chainId,
}: {
  address?: Address | string;
  search?: string;
  onClickItem?: (token: Token) => void;
  disableChains?: EChainId[];
  chainId: EChainId;
  setChainId: Dispatch<SetStateAction<EChainId>>;
}) => {
  const { user } = useAuth();
  const { data: tokenPrice } = useSolUsdPrice();
  const router = useRouter();

  const { data: tokenBalances } = useSolTokenBalance(
    user && user.solana_address ? new PublicKey(user.solana_address) : undefined
  );

  const { data: priceUsdData } = useSolUsdPrice();

  const { priceUsd } = priceUsdData || {};

  const { data: tokenWithPriceList, isLoading: isLoadingTokenPrice } = useGetSolPriceAirdrop(
    tokenBalances
      ? tokenBalances
          .filter((token) => token.mint !== SOLANA_TOKEN_ADDRESS)
          .map((item) => item.mint)
          .slice(0, 50)
      : []
  );

  const tokens = useMemo(() => {
    if (!search) {
      if (chainId === ALL_CHAIN_VALUE) {
        let allTokens: Token[] = [];
        Object.values(customChainList).map((chain) => {
          const data: Token[] = getChainTokenList(chain.id);
          allTokens = [...allTokens, ...data];
        });
        return allTokens;
      }
      return getChainTokenList(chainId);
    }
    if (chainId === ALL_CHAIN_VALUE) {
      let allTokens: Token[] = [];
      Object.values(customChainList).map((chain) => {
        const data: Token[] = getChainTokenList(chain.id).filter((token) =>
          token.symbol.toLowerCase().includes(search.toLowerCase())
        );
        allTokens = [...allTokens, ...data];
      });
      return allTokens;
    }
    return getChainTokenList(chainId).filter((token) =>
      token.symbol.toLowerCase().includes(search.toLowerCase())
    );
  }, [chainId, search]);

  const filteredTokens = useMemo(() => {
    if (address && isValidSolanaAddress(address))
      return tokens.filter((token) => token.symbol === 'SOL');
    if (address) return tokens.filter((token) => token.symbol !== 'SOL');
    return tokens;
  }, [tokens, address]);

  const parsedTokenList = useMemo(() => {
    if (!tokenBalances) return [];

    const nativeToken = filteredTokens.map((token: Token) => {
      const currentBalanceInfo = tokenBalances?.find(
        (tokenData) => tokenData.mint === token.address
      );
      const tokenUsdPrice =
        priceUsd && currentBalanceInfo
          ? new BigNumber(tokenPrice?.priceUsd || 0).multipliedBy(
              new BigNumber(currentBalanceInfo.tokenAmount.uiAmountString)
            )
          : 0;
      return {
        ...token,
        solPrice: 1,
        tokenBalance: currentBalanceInfo
          ? new BigNumber(currentBalanceInfo.tokenAmount.uiAmountString)
          : new BigNumber(0),
        tokenUsdBalance: tokenUsdPrice,
      };
    });

    const memeList = tokenWithPriceList
      ? tokenWithPriceList?.map((token) => {
          const parsedToken = {
            symbol: token.symbol.toUpperCase() as ETokenSymbol,
            name: token.name,
            icon: token.iconUri,
            chainId: isMainnet ? EChainId.Solana : EChainId.SolanaDevnet,
            decimal: 6,
            address: token.tokenAddress as Address,
          };

          const currentBalanceInfo = tokenBalances?.find(
            (tokenData) => tokenData.mint === token.tokenAddress
          );

          const tokenUsdPrice =
            priceUsd && currentBalanceInfo && token.solPrice
              ? new BigNumber(token.solPrice)
                  .multipliedBy(new BigNumber(priceUsd))
                  .multipliedBy(new BigNumber(currentBalanceInfo.tokenAmount.uiAmountString))
              : 0;

          return {
            ...parsedToken,
            solPrice: token.solPrice || 0,
            bondingCurve: token.bondingCurve,
            tokenBalance: currentBalanceInfo
              ? new BigNumber(currentBalanceInfo.tokenAmount.uiAmountString)
              : 0,
            tokenUsdBalance: tokenUsdPrice,
          };
        })
      : [];
    return [...nativeToken, ...memeList].sort(
      (a, b) => new BigNumber(b.tokenUsdBalance).comparedTo(new BigNumber(a.tokenUsdBalance)) ?? 0
    );
  }, [tokenWithPriceList, filteredTokens, tokenBalances, priceUsd, tokenPrice]);

  const showChainContent = useMemo(() => {
    if (!filteredTokens.length)
      return (
        <div className={'flex flex-col py-3 gap-3 items-center justify-center'}>
          <span className={'text-[#999]'}>No Data.</span>
        </div>
      );

    const isNotConnectedSol =
      [EChainId.Solana, EChainId.SolanaDevnet].includes(chainId) && !user?.solana_address;
    const isNotConnectedTon =
      [EChainId.Ton, EChainId.TonTestnet].includes(chainId) && !user?.ton_address;

    if (isNotConnectedSol || isNotConnectedTon) {
      return (
        <div className="flex flex-col items-center py-3 gap-5">
          <Image
            src="/images/chain-thumb.png"
            alt="chain thumb"
            width={140}
            height={140}
            className="size-[140px] object-cover"
          />

          {isNotConnectedTon && <TonConnectBtn />}
        </div>
      );
    }

    return (
      <>
        {/* {filteredTokens.map((token: Token, index: number) => {
          const currentBalanceInfo = tokenBalances?.find(
            (tokenData) => tokenData.mint === token.address
          );
          const tokenUsdPrice =
            priceUsd && currentBalanceInfo
              ? new BigNumber(tokenPrice?.priceUsd || 0).multipliedBy(
                  new BigNumber(currentBalanceInfo.tokenAmount.uiAmountString)
                )
              : 0;
          return (
            <TokenItem
              key={token.symbol + index}
              price={tokenPrice ? Number(tokenPrice.priceUsd) : 0}
              tokenBalance={
                currentBalanceInfo ? (
                  <NumberDisplay number={currentBalanceInfo.tokenAmount.uiAmountString} />
                ) : (
                  '0'
                )
              }
              tokenUsdBalance={
                priceUsd && currentBalanceInfo ? <NumberDisplay number={tokenUsdPrice} /> : '???'
              }
              token={token}
              onClick={() => onClickItem?.(token)}
            />
          );
        })} */}
        <FetchDataStatementWrapper isLoading={isLoadingTokenPrice}>
          {parsedTokenList.map((token) => {
            return (
              <TokenItem
                key={token.address}
                token={token}
                tokenBalance={
                  token.tokenBalance ? <NumberDisplay number={token.tokenBalance} /> : '0'
                }
                tokenUsdBalance={
                  token.tokenUsdBalance ? (
                    <NumberDisplay number={token.tokenUsdBalance} isNormalize />
                  ) : (
                    '???'
                  )
                }
                onClick={() => {
                  if (!('bondingCurve' in token)) {
                    onClickItem?.(token);
                    return;
                  }
                  if (token.bondingCurve === 100) {
                    router.push(routePaths.walletMemeDetail(token.address));
                  } else {
                    router.push(routePaths.memePadDetail(token.address));
                  }
                }}
              />
            );
          })}
        </FetchDataStatementWrapper>
      </>
    );
  }, [
    chainId,
    filteredTokens,
    onClickItem,
    tokenPrice,
    user,
    tokenBalances,
    priceUsd,
    isLoadingTokenPrice,
    tokenWithPriceList,
    router,
    parsedTokenList,
  ]);

  return (
    <div className={'flex flex-col gap-4'}>
      <div>{showChainContent}</div>
    </div>
  );
};
