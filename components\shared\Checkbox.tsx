import clsx from 'clsx';
import React from 'react';

type CheckboxProps = {
  label?: string | React.ReactNode;
  containerClassName?: string;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function Checkbox({ label, containerClassName, ...props }: CheckboxProps) {
  return (
    <div className={clsx('flex', containerClassName)}>
      <input
        type="checkbox"
        {...props}
        className={clsx(props.className || 'custom-checkbox disabled:cursor-not-allowed')}
      />
      {label && <label htmlFor="">{label}</label>}
    </div>
  );
}
