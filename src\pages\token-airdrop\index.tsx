import ActionButton from '@/components/core/action-button';
import PageTitle from '@/components/core/page-title';
import { Button } from '@/components/ui';
import { TITLE_FORM } from '@/constants';
import { EAirdropConditionType, EGasFeePaymentType } from '@/constants/airdrop';
import { airdropSchema, AirdropSchemaFormType } from '@/schema/airdrop';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import ConfigAirdropInfoForm from './config-airdrop-info-form';
import PreviewAirdrop from './preview-airdrop';
import useConnectPhantom from '@/hooks/use-connect-phantom';
import { usePostAirdropEventConfig } from '@/services/airdrop/airdrop.queries';

const defaultValues = {
  tokenAirdrop: '',
  airdropQuantity: 0,
  airdropAddress: [],
  airdropCondition: { type: EAirdropConditionType.TOKEN_HOLDERS, data: [] },
  description: '',
  descriptionImages: [],
  socials: [],
  eventName: '',
  airdropStartAt: undefined,
  gasFeePaymentType: EGasFeePaymentType.WALLET_AIRDROP,
};

enum EAirdropTab {
  DETAIL,
  PREVIEW,
}

export default function CreateAirdropPage() {
  const { connected, connect } = useConnectPhantom();
  const [currentTab, setCurrentTab] = useState<EAirdropTab>(EAirdropTab.DETAIL);
  const { mutate: postAirdropEventConfig } = usePostAirdropEventConfig();
  const methods = useForm<AirdropSchemaFormType>({
    resolver: zodResolver(airdropSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { errors },
  } = methods;
  console.log('🚀 ~ CreateAirdropPage ~ errors:', errors);

  const onSubmit = (data: AirdropSchemaFormType) => {
    console.log({ data });
    if (currentTab === EAirdropTab.DETAIL) {
      setCurrentTab(EAirdropTab.PREVIEW);
      return;
    }
    // postAirdropEventConfig({
    //   ...data,
    // });
  };

  if (!connected) {
    return (
      <div className="w-full h-[calc(100vh-142px)] overflow-y-auto border border-gray-9 bg-white rounded-[28px] p-7">
        <div className="flex flex-col">
          <Button variant="primary" onClick={connect}>
            Connect Wallet
          </Button>
        </div>
      </div>
    );
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <PageTitle
          title={TITLE_FORM}
          rightButton={
            currentTab === EAirdropTab.DETAIL ? (
              <Button
                className="rounded-[14px] h-9 w-[77px] flex flex-col justify-center items-center text-sm font-semibold"
                variant="primary"
                type="submit"
              >
                Next
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  className="rounded-[14px] h-9 w-[77px] flex flex-col justify-center items-center text-sm font-semibold"
                  variant="primary"
                  type="button"
                  onClick={() => setCurrentTab(EAirdropTab.DETAIL)}
                >
                  Previous
                </Button>
                <ActionButton actionType={'submit'} />
              </div>
            )
          }
        />
        <div className="w-full h-[calc(100vh-142px)] overflow-y-auto border border-gray-9 bg-white rounded-[28px] p-7">
          {currentTab === EAirdropTab.DETAIL && <ConfigAirdropInfoForm />}
          {currentTab === EAirdropTab.PREVIEW && <PreviewAirdrop />}
        </div>
      </form>
    </FormProvider>
  );
}
