import { EAirdropStatus, EEventAirdropStatus } from '@/services/airdrop';
import Image from 'next/image';
import { useMemo } from 'react';
import SvgCircle from '../shared/SvgCircle';
import { formatDate } from 'date-fns';

import QuestionMarkIcon from '@/public/icons/question-mark-icon.svg';
import clsx from 'clsx';
import Button, { BUTTON_SIZE } from '../shared/Button';
import { getBtnClass } from './common';
import { useRouter } from 'next/navigation';
import { routePaths } from '@/constants/common';

interface Props {
  icon?: string;
  progress?: number;
  title: string;
  state: EEventAirdropStatus;
  date: string | Date;
  balance: string | number;
  symbol?: string;
}

const getStyle = (state: EEventAirdropStatus) => {
  switch (state) {
    case EEventAirdropStatus.CLAIM:
      return 'bg-green-600/10 text-green-600';
    case EEventAirdropStatus.SOON:
      return 'bg-primary-500/10 text-primary-500';
    case EEventAirdropStatus.DONE:
      return 'bg-gray-300 text-gray-800';
  }
};

export default function EventAirdropItem({
  icon,
  progress = 0,
  title,
  state,
  date,
  balance,
  symbol,
}: Props) {
  const router = useRouter();
  const subTitle = useMemo(() => {
    switch (state) {
      case EEventAirdropStatus.CLAIM:
        return `Available: ${balance} ${symbol}`;
      case EEventAirdropStatus.SOON:
        return `Snapshot: ${formatDate(date, 'MMM dd')}`;
      case EEventAirdropStatus.DONE:
        return `Completed: ${formatDate(date, 'MMM dd')}`;
    }
  }, [state]);

  const onRedirect = () => {};

  const RightContent = useMemo(
    () =>
      function RightContent() {
        switch (state) {
          case EEventAirdropStatus.DONE:
            return (
              <span className="input-text-gray-600-black text-sm leading-[17px] font-medium">
                +{balance} {symbol}
              </span>
            );
          default:
            return (
              <Button
                size={BUTTON_SIZE.SMALL}
                className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.CLAIM))}
                onClick={() => router.push(routePaths.airdropEventDetail('1'))}
              >
                Check
              </Button>
            );
        }
      },
    [state]
  );

  return (
    <div className="py-3 px-[14px] bg-white rounded-std flex items-center justify-between gap-[18px]">
      <div className="relative">
        <SvgCircle progress={progress} />
        <div
          className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center cursor-pointer"
          onClick={onRedirect}
        >
          {icon ? (
            <Image
              src={icon}
              alt={'Airdrop Logo'}
              width={48}
              height={48}
              className="rounded-full size-12 aspect-square object-cover"
            />
          ) : (
            <div className="rounded-full size-12 bg-gradient-to-b from-[#ffab00] to-[#ff4b00] flex justify-center items-center">
              <QuestionMarkIcon />
            </div>
          )}
        </div>
      </div>

      <div className="grow flex items-center justify-between">
        <div className="flex flex-col gap-1.5">
          <span className="text-gray-900 font-medium cursor-pointer" onClick={onRedirect}>
            {title || '???'}
          </span>
          <span className={clsx('text-xs leading-[17px] py-1 px-2.5 rounded-lg', getStyle(state))}>
            {subTitle}
          </span>
        </div>
        <RightContent />
      </div>
    </div>
  );
}
