import { Rank } from '@/components/shared/RankIcon';
import { IContent } from '../content';

export interface ILoginResponse {
  userId: string;
  tokenType: string;
  walletAddress: string;
  accessToken: string;
}

export type UserInfoTelegram = {
  accessToken: string;
  web3AuthToken: string;
  tokenType: string;
  uniqueCode: string;
  nonce: string;
  telegramId: string;
  telegram_username: string;
  // user: IAuthUser;
};

// enum FirstTaskStatus {
//   INCOMPLETE
//   STARTED
//   FINISHED
// }

export enum FistTaskStatus {
  INCOMPLETE = 'INCOMPLETE',
  STARTED = 'STARTED',
  FINISHED = 'FINISHED',
}
export interface IAuthUser {
  address: string | null;
  user_id: string | null;
  created_at: string | Date;
  description: string | null;
  display_cover: string | null;
  display_icon: string | null;
  display_name: string | null;
  id: number;
  inviter_code: string | null;
  nonce: string | null;
  point: number;
  ref_code: string;
  sp_point: number;
  sp_point_updated_at: string | Date | null;
  total_hold_day: number;
  twitter_uid: string | null;
  twitter_username: string | null;
  updated_at: string | Date;
  invited_at: string | Date;
  youtube_id: string | null;
  uuid: string;
  private_key: string | null;
  telegram_id: string | null;
  telegram_username: string | null;
  first_task_status: FistTaskStatus | null;
  rocket: number | null;
  ton_address: string | null;
  solana_address: string | null;
  is_moved: boolean;
}

export interface ILoginPayload {
  walletAddress: string;
  signature: string;
  metaMaskMessage: string;
}

export interface ILoginTelegramPayload extends ILoginPayload {
  telegram_id: string;
  telegram_username: string;
  nonce: string;
}

export interface IProcessTask {
  ratio_do_event: number;
  events: IContent[];
}

export type FileNameGenerateUrl = {
  fileNames: string[];
};

export type PresignedUrl = {
  fileName: string;
  presignedUrl: string;
};

export type EditProfileForm = {
  display_name?: string;
  display_cover?: string;
  display_icon?: string;
  description?: string;
  private_key?: string;
};

export type ILinkTwitterPayload = {
  twitter_id: string;
  username: string;
};

export type ITaskRanking = {
  percent: number;
  type: Rank;
  title: string;
};

export type ILinkWallet = {
  walletAddress: string;
  signature: string;
  metaMaskMessage: string;
};

export type ILinkTelegram = {
  code: string;
};

export type IResponseLinkTele = {
  message: string;
  telegramId: string;
};

export type IVerifyCompleteTaskPayload = {
  task_id: number;
  verify_code?: string;
  chain_id: string;
  token_image?: string;
  background_image_id?: number;
  part_image_id?: number;
};

export type IUserRank = {
  ranking: number;
  rankName: Rank;
  yesterdayRank: number;
  rankingChange: number;
  totalUser: number;
};

export type IMemePadWaitListPayload = {
  solana_address: string;
  email: string;
};

export type ISolanaLoginResponse = {
  accessToken: string;
  tokenType: string;
  walletAddress: string;
  solanaAddress: string;
  userId: number;
};

export type ILoginWithSolanaPayload = {
  solana_address: string;
  signature: string;
  message: string;
};

export interface INewUserPointInfo {
  user_id: number;
  count_score_0: number;
  point_score_0: number;
  count_score_1: number;
  point_score_1: number;
  count_score_2: number;
  point_score_2: number;
  total_point: number;
  old_rocket: number;
}

export interface INewUserRankingDetail {
  id: number;
  user_id: number;
  point: number;
  today_point: number | null;
  swap_point: number | null;
  old_point: number;
  created_at: string | Date;
  updated_at: string | Date;
}

export interface ISwapPointParam {
  hash: string;
  token_amount: number;
  usd_amount: number;
  token_in: string;
  token_out: string;
}
