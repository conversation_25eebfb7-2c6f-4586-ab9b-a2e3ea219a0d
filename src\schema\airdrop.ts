import { z } from 'zod';
import { EGasFeePaymentType, EAirdropConditionType } from '@/constants/airdrop';
import { getFlexibleSocialUrlErrorMessage, isValidSocialUrl } from '@/lib/utils';

export const airdropSchema = z
  .object({
    tokenAirdrop: z.string().min(1, 'Token address is required'),
    airdropQuantity: z.number().gt(0, 'Airdrop Quantity is required'),
    airdropAddress: z.array(z.string()).optional(),
    airdropCondition: z.object({
      type: z.enum(Object.values(EAirdropConditionType) as [string, ...string[]]),
      data: z.array(z.string()),
    }),
    description: z.string(),
    descriptionImages: z.array(z.string()).optional(),
    socials: z
      .array(
        z.object({
          name: z.string().optional(),
          url: z.string().optional(),
        })
      )
      .optional(),
    eventName: z.string().optional(),
    airdropStartAt: z.date({ required_error: 'Start date is required' }),
    gasFeePaymentType: z
      .enum([EGasFeePaymentType.IN_APP, EGasFeePaymentType.WALLET_AIRDROP])
      .optional(),
  })
  .superRefine((data, ctx) => {
    if (!Number(data.airdropQuantity)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['airdropQuantity'],
        message: 'This field must be greater than 0.',
      });
    }
    data.socials?.forEach((social, index) => {
      const { name, url } = social;
      if (url && typeof url !== 'string') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['socials', index, 'url'],
          message: 'Invalid URL.',
        });
      }
      if (url && url.trim().length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['socials', index, 'url'],
          message: 'Field cannot be only whitespace',
        });
      }

      if (
        url &&
        (!isValidSocialUrl(url.trim(), name) ||
          !getFlexibleSocialUrlErrorMessage(url.trim(), name!))
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['socials', index, 'url'],
          message: 'Invalid Social Url',
        });
      }
    });
  });

export type AirdropSchemaFormType = z.infer<typeof airdropSchema>;
