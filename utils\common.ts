import { toast } from 'react-toastify';
import { format, formatDate } from 'date-fns';
import clsx, { ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { EStorageKeys } from '@/lib/storage';
import { jwtDecode } from 'jwt-decode';
import { Api } from '@/lib/axios';
import {
  Keypair,
  LAMPORTS_PER_SOL,
  ParsedInstruction,
  ParsedTransactionWithMeta,
  PublicKey,
} from '@solana/web3.js';
import { Address } from 'viem';
import { TransferHistory } from '@/lib/web3/alchemy';
import { chainList, EChainId } from '@/lib/web3/constants';
import { SI } from '@/constants/memePad';
import { BigNumber } from 'bignumber.js';
import { ICoin } from '@/services/meme-launchpad';
import { convertMistToDec } from './helper';

export const formatAddress = (address: string, num: number) => {
  if (!address) return '';
  if (address.length <= num * 2) return address;
  const format = address.slice(0, num) + '...' + address.slice(-num);
  return format;
};

export const formatTelegramId = (address: string, num: number) => {
  if (!address) return '';
  if (address.length <= num * 2) return address;
  const format = address.slice(0, num) + '...' + address.slice(-2);
  return format;
};

export const validateId = (id: string, numChars: number): string => {
  if (!id) return id;
  let newId = id.replace(/-/g, '').toUpperCase();

  // IDの長さが、残すべき文字数の2倍以下の場合は、省略せずに全文字を返す
  if (newId.length <= numChars * 2) return newId;

  // 指定された数の文字を前後から残し、残りを "..." で置き換える
  return newId.slice(0, numChars) + '...' + newId.slice(-numChars);
};

export function copyTextToClipboard(text: string | number) {
  navigator.clipboard.writeText(String(text)).then(
    function () {
      toast.success('Copying to clipboard was successful!');
    },
    function (err) {
      toast.error('Could not copy text: ', err);
    }
  );
}

export const formatNumberWithCommas = (x: number | string, decimalPlaces?: number): string => {
  if (typeof x !== 'number' && !x) return '';
  const [integerPart, decimalPart] = decimalPlaces
    ? Number(x).toFixed(decimalPlaces).toString().split('.')
    : Number(x).toString().split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const findKeyFromValue = (obj: Record<string, any>, value: any): string | undefined => {
  return Object.entries(obj).find(([, val]) => val === value)?.[0];
};

export const convertToNormalText = (str: string): string => {
  if (!str) return '';
  return str
    .toLowerCase()
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const formatDateTime = (
  date: Date | string | number | undefined,
  dateFormat?: string
): string => {
  if (!date) return '';
  return formatDate(date, dateFormat ?? 'dd/MM/yyyy');
};

export const isImgUrl = (url: string | undefined) => {
  if (!url) return false;
  return /\.(jpg|jpeg|png|webp|avif|gif)$/.test(url);
};

export const cn = (...classes: ClassValue[]) => {
  return twMerge(clsx(classes));
};

export const getNftMarketLink = (chain: string, contractAddress: string, nftId: string): string => {
  return `https://opensea.io/assets/${chain}/${contractAddress}/${nftId}`;
};

export const isYoutubeShortVideoLink = (url: string): boolean => {
  return url.includes('shorts');
};

export const revalidateCache = (url: string = '/') => {
  if (url) {
    fetch(`${process.env.NEXT_PUBLIC_HOST}/api/revalidate?path=${url}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

export const getExpiredDate = () => {
  if (typeof window === 'undefined') return null;
  const token = localStorage.getItem(EStorageKeys.accessToken);
  if (!token) return null;
  const decodedToken: { exp: number } = jwtDecode(token);
  return new Date(decodedToken.exp * 1000).getTime();
};

export const roundNumber = (value: number, decimalPlaces: number): number => {
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(value * factor) / factor;
};

export const generateRandomString = (length: number) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }

  return result;
};

declare global {
  interface Window {
    Telegram: {
      WebView: {
        onEvent: (
          eventName: string,
          params: (
            eventName: string,
            params: {
              height: number;
              is_expanded: boolean;
              is_state_stable: boolean;
            }
          ) => void
        ) => void;
      };
      WebApp: {
        viewportHeight: number;
        openInvoice: (url: string) => void;
        openLink: (url: string) => void;
        initDataUnsafe: {
          start_param: string;
          user: {
            id: number | undefined;
          };
        };
        version: string;
        initData: string;
        downloadFile: (params: { url: string; file_name: string }) => void;
      };
    };
  }
}

export const openLinkInTelegram = (url: string | undefined) => {
  if (!url) return;
  if (window.Telegram.WebApp?.initData) {
    window.Telegram.WebApp?.openLink(url);
  } else {
    window.open(url, '_blank');
  }
};

export const getStartParam = () => {
  if (window.Telegram?.WebApp) {
    return window.Telegram.WebApp.initDataUnsafe.start_param;
  }
  return '';
};

export const getTelegramUserId = () => {
  if (window.Telegram?.WebApp) {
    return window.Telegram.WebApp.initDataUnsafe.user;
  }
  return '';
};

const telegramRegex = /^https?:\/\/t\.me\/[-a-zA-Z0-9.]+(\/\S*)?$/;

export const isTelegramLink = (url: string) => {
  return telegramRegex.test(url);
};
export const isTelegramApp = () => {
  // Check if the user agent string contains "Telegram"
  return typeof window !== 'undefined' && /Telegram/i.test(navigator.userAgent);
};

export const isTelegramWebApp = () => {
  // Check if the user agent string contains "Telegram"
  return (
    typeof window !== 'undefined' &&
    window.Telegram &&
    window.Telegram.WebApp &&
    window.Telegram.WebApp.initData
  );
};

export const navigateTelegram = (url: string) => {
  const isTgLink = isTelegramLink(url);
  //@ts-ignore
  const inTelegram = !!window.Telegram.WebApp.initData;
  // alert('isTelegramApp' + inTelegram);
  if (inTelegram && isTgLink) {
    // alert('isTelegramApp + isTgLink');
    // if (isTgLink) {
    //@ts-ignore
    window.Telegram.WebApp?.openTelegramLink(url);
    // } else {
    //   window.Telegram.WebApp.openLink(url);
    // }
  } else {
    //@ts-ignore
    window.Telegram.WebApp?.openLink(url, { try_instant_view: true });
  }
};

export const toDataURL = async (url: string) => {
  const blob = await fetch(url).then((res) => res.blob());
  return URL.createObjectURL(blob);
};

export const saveFile = async (url: string, name: string) => {
  if (window?.Telegram?.WebApp?.initData) {
    try {
      window.Telegram.WebApp?.downloadFile({ url, file_name: name });
    } catch (e) {
      console.error(e);
      if (e === 'WebAppMethodUnsupported') {
        toast.error(
          '[Telegram.WebApp] Method downloadFile is not supported in version ' +
            window.Telegram.WebApp?.version
        );
        return;
      }
      toast.error('Failed to download the file');
    }
    return;
  }
  try {
    const splittedUrl = url.split('/');
    const splittedUrlLength = splittedUrl.length;
    const res = await Api.get('/download-base64', {
      params: {
        key: splittedUrl[splittedUrlLength - 1],
      },
    });
    const base64Response = await fetch(`data:image/png;base64,${res}`);
    const blob = await base64Response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    // const link = document.createElement('a');
    // link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (e) {
    console.error(e);
    toast.error('Failed to download the file');
  }
};

export const generateUUID = () => {
  let d = new Date().getTime();
  let d2 = (performance && performance.now && performance.now() * 1000) || 0;
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16;
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
};

export const isValidSolanaAddress = (address: string): boolean => {
  try {
    const key = new PublicKey(address);
    return Boolean(PublicKey.isOnCurve(key.toBytes()));
  } catch (e) {
    return false;
  }
};

export const parsedSolanaTransaction = (
  address: string,
  transaction: ParsedTransactionWithMeta
) => {
  const instructions = transaction.transaction.message.instructions;

  const validInstruction = instructions.find((instruction) => {
    if ('accounts' in instruction) {
      return instruction.accounts.find((key) => key.toBase58() === address);
    }
    return (
      'parsed' in instruction &&
      (instruction.parsed.info.source === address ||
        instruction.parsed.info.destination === address)
    );
  });

  if (!validInstruction) {
    return null;
  }

  const instruction = validInstruction;

  if (
    (instruction as ParsedInstruction).parsed &&
    (instruction as ParsedInstruction).parsed.info.lamports
  ) {
    const parsed = (instruction as ParsedInstruction).parsed.info;
    return {
      programId: instruction.programId.toBase58(),
      createdAt: transaction.blockTime ? transaction.blockTime * 1000 : '',
      last: transaction.blockTime ? transaction.blockTime * 1000 : '',
      symbol: 'SOL',
      value: parsed.lamports / LAMPORTS_PER_SOL || 0,
      hash: transaction.transaction.signatures[0] || '',
      type: parsed.source === address ? 'send' : 'receive',
      address: parsed.source === address ? parsed.destination : (parsed.source as Address),
    } as TransferHistory;
  } else if (transaction.meta) {
    const meta = transaction.meta;
    const changedBalance = meta.postBalances[0] - meta.preBalances[0];
    return {
      programId: instruction.programId.toBase58(),
      createdAt: transaction.blockTime ? transaction.blockTime * 1000 : '',
      last: transaction.blockTime ? transaction.blockTime * 1000 : '',
      symbol: 'SOL',
      value: Math.abs(changedBalance) / LAMPORTS_PER_SOL || 0,
      hash: transaction.transaction.signatures[0] || '',
      type: changedBalance < 0 ? 'send' : 'receive',
    } as TransferHistory;
  }
};

export const getHashUrl = (hash: string, chainId: EChainId) => {
  if (!chainId || !hash) return '';
  if ([EChainId.Solana, EChainId.SolanaDevnet].includes(chainId)) {
    const splittedUrl = chainList[chainId].blockExplorer.split('?');
    return splittedUrl[0] + '/tx/' + hash + '?' + splittedUrl[1];
  }
  return chainList[chainId].blockExplorer;
};

export const isOnlyTelegramLogin = () => {
  return (
    typeof window !== 'undefined' &&
    window.Telegram &&
    window.Telegram.WebApp &&
    (window.Telegram.WebApp.initData || !('serviceWorker' in navigator))
  );
};

export const offsetTimezone = (inputDate?: Date) => {
  const date = inputDate || new Date();
  const offsetMinutes = date.getTimezoneOffset();
  const offsetHours = -offsetMinutes / 60;
  const offsetSign = offsetHours >= 0 ? '+' : '';

  const formattedOffset = Math.abs(offsetHours) % 1 === 0 ? offsetHours : offsetHours.toFixed(1);
  return `UTC${offsetSign}${formattedOffset}`;
};

export const formatDateWithTimezone = (
  inputDate: Date | string,
  formatStyle = 'dd/MM/yy'
): string => {
  const formattedDate = format(inputDate, formatStyle);

  return `${formattedDate} ${offsetTimezone(new Date(inputDate))}`;
};

export const getStakingBonus = (stakingAmount: number, pool: number, period: number) => {
  return ((stakingAmount * pool) / 100 / 365) * period;
};

export const getSocialPlatformPrefix = (key: string): string => {
  switch (key) {
    case 'twitterLink':
      return 'https://x.com/';
    case 'telegramLink':
      return 'https://t.me/';
    case 'discordLink':
      return 'https://discord.gg/';
    case 'youtubeLink':
      return 'https://youtube.com/';
    case 'instagramLink':
      return 'https://instagram.com/';
    case 'tiktokLink':
      return 'https://tiktok.com/@';
    case 'facebookLink':
      return 'https://facebook.com/';
    case 'websiteLink':
      return 'https://';
    case 'dexScreenerLink':
      return 'https://dexscreener.com/';
    case 'dexToolsLink':
      return 'https://www.dextools.io/';
    case 'extraLink':
      return 'https://';
    default:
      return 'https://';
  }
};

/**
 * Calculate and format the age of a token from its creation date
 * @param createdAt ISO date string of token creation
 * @returns Formatted age string (e.g., "5d 2h" or "3h 45m")
 */
export const getTokenAge = (createdAt: string): string => {
  if (!createdAt) return '0d 0h';

  const now = new Date();
  const created = new Date(createdAt);
  const diffMs = now.getTime() - created.getTime();

  // Convert to days, hours, minutes
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  // Format based on age
  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};

export const formatUnixTimestamp = (
  timestamp: number,
  formatStyle = 'dd/MM/yyyy HH:mm:ss'
): string => {
  const date = new Date(timestamp * 1000);
  return format(date, formatStyle);
};

export const roundBigNumber = (
  number: number | string | BigNumber,
  decimals = 18,
  roundMode = BigNumber.ROUND_DOWN
) => {
  const newNumber = new BigNumber(number).toFixed(decimals, roundMode);
  return new BigNumber(newNumber).toString();
};

const _formatLargeNumberIfNeed = (number: string | null | undefined, digits = 0) => {
  if (!number || isNaN(Number(number))) {
    return '--';
  }
  const comparedNumber = new BigNumber(number).comparedTo(1000);

  if (comparedNumber && comparedNumber < 0) {
    // Remove trailing zeros after decimal point
    return formatNumberWithCommas(number, digits);
  }
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;

  const num = parseFloat(number);
  let i;
  for (i = SI.length - 1; i > 0; i--) {
    if (num >= SI[i].value) {
      break;
    }
  }
  // Remove trailing zeros after decimal point
  return (
    BigNumber(num / SI[i].value)
      .toFixed(digits, BigNumber.ROUND_DOWN)
      .replace(rx, '$1') + SI[i].symbol
  );
};

export function formatNumber(
  value: string | number | BigNumber,
  decimalPlaces = 8,
  defaultValue = '--'
): string {
  if (!value || new BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  if (
    new BigNumber(value).isGreaterThan(0) &&
    new BigNumber(value).isLessThan(convertMistToDec(1, decimalPlaces))
  ) {
    return '<' + new BigNumber(convertMistToDec(1, decimalPlaces)).toString();
  }

  return _formatLargeNumberIfNeed(roundBigNumber(Number(value), decimalPlaces), decimalPlaces);
}

export function formatUsdNumber(value: string | number | BigNumber, defaultValue = '--'): string {
  if (!value || new BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  if (new BigNumber(value).isGreaterThan(0) && new BigNumber(value).isLessThan(0.00000001)) {
    return '<$' + new BigNumber(0.00000001).toString();
  }

  if (new BigNumber(value).isGreaterThan(1)) {
    return formatNumber(value, 2);
  }

  const numStr = new BigNumber(value).toExponential();
  const exponent = parseInt(numStr.split('e-')[1]) || 1;

  return formatNumber(value, exponent - 1 + 4);
}

/**
 * Format a number to a compact representation (K, M, B, etc.)
 * @param value The number to format
 * @param decimals Number of decimal places to show (default: 2)
 * @param defaultValue Value to return if input is invalid (default: '0')
 * @returns Formatted string like 1K, 1.5M, 2.34B, etc.
 */
export function formatCompactNumber(
  value: number | string | BigNumber | null | undefined,
  decimals = 2,
  defaultValue = '0'
): string {
  // Handle invalid or zero values
  if (!value || new BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  const num = new BigNumber(value).toNumber();
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';

  // For small numbers, don't use compact notation
  if (absNum < 1000) {
    return sign + absNum.toFixed(decimals).replace(/\.?0+$/, '');
  }

  // Find the appropriate SI unit
  let i;
  for (i = SI.length - 1; i > 0; i--) {
    if (absNum >= SI[i].value) {
      break;
    }
  }

  // Format with the selected unit
  const scaled = absNum / SI[i].value;
  const formatted = scaled.toFixed(decimals).replace(/\.?0+$/, '');

  return sign + formatted + SI[i].symbol;
}

function visibleLength(str: string): number {
  // Loại bỏ dấu "." trước khi đếm
  return str.replace(/\./g, '').length;
}

export const formatCompactNumberWithLimitChar = (
  value: number | string | BigNumber | null | undefined,
  limitCharacter = 4,
  decimals = 10,
  defaultValue = '0'
) => {
  if (!value || new BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  const num = new BigNumber(value).toNumber();
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';

  if (absNum < 1000) {
    let str = absNum.toFixed(decimals).replace(/\.?0+$/, '');

    if (limitCharacter && visibleLength(sign + str) > limitCharacter) {
      let allowedDecimals = decimals;
      while (visibleLength(sign + str) > limitCharacter && allowedDecimals > 0) {
        allowedDecimals--;
        str = absNum.toFixed(allowedDecimals).replace(/\.?0+$/, '');
      }
    }
    return sign + str;
  }

  let i;
  for (i = SI.length - 1; i > 0; i--) {
    if (absNum >= SI[i].value) {
      break;
    }
  }

  const scaled = absNum / SI[i].value;
  let formatted = scaled.toFixed(decimals).replace(/\.?0+$/, '');
  let result = sign + formatted + SI[i].symbol;

  if (limitCharacter && visibleLength(result) > limitCharacter) {
    let allowedDecimals = decimals;
    while (visibleLength(result) > limitCharacter && allowedDecimals > 0) {
      allowedDecimals--;
      formatted = scaled.toFixed(allowedDecimals).replace(/\.?0+$/, '');
      result = sign + formatted + SI[i].symbol;
    }
  }

  return result;
};
/**
 * Calculate percentage change between current and previous values
 * Handles all edge cases including zero values, small values, and potential infinity results
 *
 * @param currentValue Current market cap value
 * @param previousValue Previous market cap value
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted percentage change with + or - sign
 */
export const calculatePercentageChange = (
  currentValue: string | number | undefined | null,
  previousValue: string | number | undefined | null,
  decimals = 2
): string => {
  // Handle missing values
  if (!currentValue || !previousValue) return '';

  // Parse values to numbers if they're strings
  const current =
    typeof currentValue === 'string' ? parseFloat(currentValue) : Number(currentValue);
  const previous =
    typeof previousValue === 'string' ? parseFloat(previousValue) : Number(previousValue);

  // Handle invalid numbers
  if (isNaN(current) || isNaN(previous)) return '';

  // Calculate percentage change even when previous is very small
  if (previous === 0) {
    // If current is also zero, no change
    // if (current === 0) return '0%';

    // If we have current value but no previous value, show a significant increase
    // Use a large but finite number
    // return current > 0 ? '+∞%' : '-∞%';
    return '';
  }

  // Handle very small previous values by using a minimum threshold
  if (Math.abs(previous) < 0.0000001) {
    const minThreshold = 0.0000001; // Minimum value to use as denominator
    const percentageChange = ((current - previous) / minThreshold) * 100;

    if (!isFinite(percentageChange) || isNaN(percentageChange)) {
      // Use a large but finite number
      return current > previous ? '+∞%' : '-∞%';
    }

    // Cap extremely large values
    if (Math.abs(percentageChange) > 9999) {
      return percentageChange > 0 ? '+∞%' : '-∞%';
    }

    return `${percentageChange > 0 ? '+' : ''}${formatCompactNumber(percentageChange, decimals)}%`;
  }

  // Standard percentage calculation for normal cases
  const percentageChange = ((current - previous) / previous) * 100;

  // Handle potential infinity or NaN cases
  if (!isFinite(percentageChange) || isNaN(percentageChange)) {
    return '';
  }

  // Cap extremely large values
  if (Math.abs(percentageChange) > 9999) {
    return percentageChange > 0 ? '+∞%' : '-∞%';
  }

  const formattedPercentageChange = formatCompactNumber(percentageChange, decimals);

  if (!Number(formattedPercentageChange)) return '';

  // Format with sign and formatCompactNumber
  return `${percentageChange > 0 ? '+' : ''}${formattedPercentageChange}%`;
};

export const calculatePercentageChangeWithLimit = (
  currentValue: string | number | undefined | null,
  previousValue: string | number | undefined | null,
  decimals = 2,
  limitCharacter?: number
): string => {
  if (!currentValue || !previousValue) return '';

  const parseNum = (val: string | number) =>
    typeof val === 'string'
      ? parseFloat(val.replace(/,/g, '.').replace(/[^0-9.+-]/g, ''))
      : Number(val);

  const current = parseNum(currentValue);
  const previous = parseNum(previousValue);

  if (isNaN(current) || isNaN(previous)) return '';

  // Handle previous = 0
  if (previous === 0) {
    if (current === 0) return '0%';
    return '';
  }

  // Handle very small previous values
  if (Math.abs(previous) < 1e-7) {
    const minThreshold = 1e-7;
    const percentageChange = ((current - previous) / minThreshold) * 100;

    if (
      !isFinite(percentageChange) ||
      isNaN(percentageChange) ||
      Math.abs(percentageChange) > 9999
    ) {
      return percentageChange > 0 ? '+∞%' : '-∞%';
    }

    const formatted = formatCompactNumberWithLimitChar(
      percentageChange,
      limitCharacter ? limitCharacter - 1 : undefined,
      decimals,
      '0'
    );
    return `${percentageChange > 0 ? '+' : ''}${formatted}%`;
  }

  // Normal calculation
  const percentageChange = ((current - previous) / previous) * 100;

  if (!isFinite(percentageChange) || isNaN(percentageChange)) return '';

  if (Math.abs(percentageChange) > 9999) {
    return percentageChange > 0 ? '+∞%' : '-∞%';
  }

  const formatted = formatCompactNumberWithLimitChar(
    percentageChange,
    limitCharacter ? limitCharacter - 1 : undefined,
    decimals,
    '0'
  );

  if (!Number(formatted)) return '';

  return `${percentageChange > 0 ? '+' : ''}${formatted}%`;
};

export const isInPhantomApp = () => {
  return (
    typeof window !== 'undefined' &&
    window.navigator &&
    window.navigator.userAgent &&
    window.navigator.userAgent.includes('Phantom')
  );
};

export const getSocialDomains = (): string[] => {
  if (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_SOCIAL_DOMAINS) {
    return process.env.NEXT_PUBLIC_SOCIAL_DOMAINS.split(',').map((d) => d.trim().toLowerCase());
  }
  // fallback default list
  return [
    'twitter.com',
    'x.com',
    't.me',
    'telegram.me',
    'discord.gg',
    'discord.com',
    'youtube.com',
    'youtu.be',
    'instagram.com',
    'tiktok.com',
    'facebook.com',
    'fb.com',
  ];
};

export const isSocialLink = (url: string): boolean => {
  if (!url) return false;
  try {
    const domains = getSocialDomains();
    const { hostname } = new URL(url);
    return domains.some((domain) => hostname.toLowerCase().endsWith(domain));
  } catch {
    return false;
  }
};

export const formatName = (name: string) => {
  if (!name) return '';

  if (name.length > 10) {
    return name.trim().slice(0, 10) + '...';
  }

  return name.trim();
};

export const formatAgeTime = (timestamp: number, suffix: string = '', prefix: string = '') => {
  const now = Date.now();
  const diffMs = now - timestamp;
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor(diffMs / (1000 * 60));
  const seconds = Math.floor(diffMs / 1000);

  if (days >= 1) {
    return `${prefix} ${days}d ${suffix}`;
  }

  if (hours >= 1) {
    return `${prefix} ${hours}h ${suffix}`;
  }

  if (minutes >= 1) {
    return `${prefix} ${minutes}m ${suffix}`;
  }

  if (seconds >= 1) {
    return `${prefix} ${seconds}s ${suffix}`;
  }

  return 'just now';

  // return '--';
};

export const openNewTabWithAnchor = (url: string) => {
  const anchor = document.createElement('a');
  anchor.href = url;
  anchor.target = '_blank';
  anchor.rel = 'noopener noreferrer';
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
};

/**
 * Gets a user-friendly error message for invalid social media URLs
 * @param platform The social media platform key
 * @returns Error message with acceptable URL formats
 */
export const getSocialUrlErrorMessage = (platform: string): string => {
  const messages: Record<string, string> = {
    twitterLink: 'Must be a valid Twitter or X URL (e.g., twitter.com/username or x.com/username)',
    telegramLink: 'Must be a valid Telegram URL (e.g., t.me/username or telegram.me/username)',
    discordLink:
      'Must be a valid Discord invite URL (e.g., discord.gg/invite or discord.com/invite)',
    youtubeLink: 'Must be a valid YouTube URL (e.g., youtube.com/channel or youtu.be/video)',
    instagramLink: 'Must be a valid Instagram URL (e.g., instagram.com/username)',
    tiktokLink: 'Must be a valid TikTok URL (e.g., tiktok.com/@username)',
    facebookLink: 'Must be a valid Facebook URL (e.g., facebook.com/username or fb.com/username)',
    websiteLink: 'Must be a valid website URL',
    dexScreenerLink: 'Must be a valid DexScreener URL (e.g., dexscreener.com/...)',
    dexToolsLink: 'Must be a valid DexTools URL (e.g., dextools.io/...)',
    extraLink: 'Must be a valid URL',
  };

  return messages[platform] || 'Must be a valid URL';
};

export const getRaydiumDirectLink = (tokenAddress: string) => {
  return `https://raydium.io/swap/?inputMint=${tokenAddress}&outputMint=sol`;
};

/**
 * More flexible validation for social media URLs
 * Supports both http and https protocols, with or without www prefix,
 * and alternative domain names for the same platform (like Twitter/X)
 *
 * @param url The URL to validate
 * @param socialType Optional social media type to specifically validate against
 * @returns Boolean indicating if the URL is valid
 */
export const isValidSocialUrl = (url: string, socialType?: string): boolean => {
  if (!url) return false;

  try {
    // First check if it's a valid URL format
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Handle protocol (both http and https are valid)
    if (!urlObj.protocol.match(/^https?:/)) {
      return false;
    }

    // Remove www. prefix if present
    const normalizedHostname = hostname.replace(/^www\./, '');

    // If a specific social type is provided, validate against that
    if (socialType) {
      switch (socialType) {
        case 'twitterLink':
          return normalizedHostname === 'twitter.com' || normalizedHostname === 'x.com';
        case 'telegramLink':
          return normalizedHostname === 't.me' || normalizedHostname === 'telegram.me';
        case 'discordLink':
          return normalizedHostname === 'discord.gg' || normalizedHostname === 'discord.com';
        case 'youtubeLink':
          return normalizedHostname === 'youtube.com' || normalizedHostname === 'youtu.be';
        case 'instagramLink':
          return normalizedHostname === 'instagram.com';
        case 'tiktokLink':
          return normalizedHostname === 'tiktok.com';
        case 'facebookLink':
          return normalizedHostname === 'facebook.com' || normalizedHostname === 'fb.com';
        case 'websiteLink':
          return true; // Any valid URL is acceptable for website
        case 'dexScreenerLink':
          // return normalizedHostname === 'dexscreener.com';
          return true;
        case 'dexToolsLink':
          // return normalizedHostname === 'dextools.io';
          return true;
        default:
          return true; // For extraLink or unknown types, any valid URL is acceptable
      }
    }

    // If no specific type, check against all known social domains
    const domains = getSocialDomains();
    return domains.some((domain) => {
      const normalizedDomain = domain.replace(/^www\./, '');
      return (
        normalizedHostname === normalizedDomain ||
        normalizedHostname.endsWith(`.${normalizedDomain}`)
      );
    });
  } catch {
    return false;
  }
};

/**
 * Gets a user-friendly error message for invalid social media URLs
 * with more detailed information about acceptable formats
 *
 * @param url The URL that failed validation
 * @param socialType The social media platform key
 * @returns Error message with acceptable URL formats
 */
export const getFlexibleSocialUrlErrorMessage = (url: string, socialType: string): string => {
  if (!url) return 'URL is required';

  // First check if it's a valid URL format at all
  try {
    new URL(url);
  } catch {
    return 'Must be a valid URL (e.g., https://example.com)';
  }

  // If it's a valid URL but not a valid social URL for the specific type
  const messages: Record<string, string> = {
    twitterLink:
      'Must be a valid Twitter or X URL (e.g., https://twitter.com/username or https://x.com/username)',
    telegramLink:
      'Must be a valid Telegram URL (e.g., https://t.me/username or https://telegram.me/username)',
    discordLink:
      'Must be a valid Discord invite URL (e.g., https://discord.gg/invite or https://discord.com/invite)',
    youtubeLink:
      'Must be a valid YouTube URL (e.g., https://youtube.com/channel or https://youtu.be/video)',
    instagramLink: 'Must be a valid Instagram URL (e.g., https://instagram.com/username)',
    tiktokLink: 'Must be a valid TikTok URL (e.g., https://tiktok.com/@username)',
    facebookLink:
      'Must be a valid Facebook URL (e.g., https://facebook.com/username or https://fb.com/username)',
    websiteLink: 'Must be a valid website URL',
    dexScreenerLink: 'Must be a valid DexScreener URL (e.g., https://dexscreener.com/...)',
    dexToolsLink: 'Must be a valid DexTools URL (e.g., https://dextools.io/...)',
    extraLink: 'Must be a valid URL',
  };

  return messages[socialType] || 'Must be a valid URL';
};

/**
 * Converts a date string to a timestamp in milliseconds
 * @param dateString ISO date string or any valid date string format
 * @returns Timestamp in milliseconds or 0 if invalid
 */
export const dateStringToTimestamp = (dateString: string | undefined | null): number => {
  if (!dateString) return 0;

  try {
    const date = new Date(dateString);
    return date.getTime();
  } catch (error) {
    console.error('Error converting date string to timestamp:', error);
    return 0;
  }
};

export const parsedChainErrorMessage = (error: string) => {
  if (!error) {
    return { errorMessage: 'Something went wrong. Please try again later.' };
  }
  const regex = /Error Number:\s*(\d+)\.?\s*Error Message:\s*([^.]+)/;
  const match = error.match(regex);
  if (match && match.length === 3) {
    const errorNumber = match[1];
    const errorMessage = match[2];
    return {
      errorNumber,
      errorMessage,
    };
  }
  return { errorMessage: 'Something went wrong. Please try again later.' };
};

export function formatNumberWithSubscriptZeros(
  numberStr: string,
  prediction = 3,
  min = 0.001
): string {
  const number = parseFloat(numberStr);
  if (number >= min) {
    const [part0, part1] = numberStr.split('.');
    if (part1) {
      const leadingZeros = part1?.match?.(/^0+/)?.[0] || '';
      return `${part0}.${leadingZeros}${part1.replace(leadingZeros, '').slice(0, prediction)}`;
    }
    return part1 ? [part0, part1.slice(0, prediction)].join('.') : part0;
  }

  const leadingZerosMatch = numberStr.match(/^0\.(0+)/);
  if (!leadingZerosMatch) return numberStr;

  const leadingZerosCount = leadingZerosMatch[1].length;
  const remainingDigits = numberStr.slice(leadingZerosMatch[0].length);

  const smallCount = String(leadingZerosCount)
    .split('')
    .map((digit) => String.fromCharCode(8320 + parseInt(digit)))
    .join('');

  return `0.0${smallCount}${remainingDigits.slice(0, prediction)}`;
}

/**
 * Generate a Solana Keypair whose public key ends with the given suffix.
 * WARNING: This is a brute-force operation and can take a long time for longer suffixes.
 * @param suffix The string the public key should end with (e.g. "abc")
 * @returns Promise<Keypair>
 */
export async function generateKeypairWithSuffix(suffix: string): Promise<Keypair> {
  suffix = suffix.toLowerCase();
  let attempts = 0;
  let found = false;
  while (!found) {
    const keypair = Keypair.generate();
    const pubkeyBase58 = keypair.publicKey.toBase58().toLowerCase();
    if (pubkeyBase58.endsWith(suffix)) {
      return keypair;
    }
    attempts++;
    // Optionally, yield to event loop every 1000 attempts to avoid blocking UI
    if (attempts % 1000 === 0) await new Promise((r) => setTimeout(r, 0));
  }
  // This should never be reached, but added to satisfy TypeScript
  throw new Error('Failed to generate keypair with the specified suffix.');
}

export const isVerifiedMeme = (token?: ICoin) => {
  if (!token || !token.tokenAddress) return false;
  return token.isVerified;
};

export const getBondingCurvePrecision = (number: number) => {
  if (number < 10) return 2;
  return 1;
};

export const durationFormatter = (duration: number[] = [], separate = ':') => {
  if (!duration.length) return;
  return duration.map((item) => item.toString().padStart(2, '0')).join(separate);
};
