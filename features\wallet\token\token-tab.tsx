import { EChainId, isMainnet, SOLANA_TOKEN_ADDRESS, Token } from '@/lib/web3/constants';
import { useRouter } from 'next/navigation';
import { ReactNode, useMemo, useState } from 'react';
import { TokenListing } from './token-listing';

// import SendIcon from '@/public/icons/send-icon.svg';
import { routePaths } from '@/constants/common';
// import { EStep } from './send';
// import DepositIcon from '@/public/icons/deposit-icon.svg';
// import StakingIcon from '@/public/icons/old-staking-icon.svg';
// import { Segmented } from '@/components/shared';
// import { HistoryTrx } from './history-trx';
// import useUsdBalance from '@/hooks/use-usd-balance';
// import { formatNumberWithCommas } from '@/utils/common';
// import ConnectorSelect from '@/components/shared/ConnectorSelect';
import clsx from 'clsx';
import { useAuth } from '@/hooks/use-auth';
import { PublicKey } from '@solana/web3.js';
import useSolTokenBalance from './hooks/use-sol-token-balance';
import { useSolUsdPrice } from '@/services/meme-launchpad';
import BigNumber from 'bignumber.js';
import { useGetSolPriceAirdrop } from '@/services/airdrop';
import { formatNumberWithCommas, formatNumberWithSubscriptZeros } from '@/utils/common';
import { Segmented } from '@/components/shared';
import { HistoryTrx } from './history-trx';
// import SwapIcon from '@/public/icons/24x24-menu-icon/swap-icon.svg';

export const TokenTab = () => {
  const router = useRouter();

  const { user } = useAuth();

  const { data: tokenBalances } = useSolTokenBalance(
    user && user.solana_address ? new PublicKey(user.solana_address) : undefined
  );

  const { data: priceUsdData } = useSolUsdPrice();
  const { priceUsd } = priceUsdData || {};

  const { data: tokenWithPriceList, isLoading: isLoadingTokenPrice } = useGetSolPriceAirdrop(
    tokenBalances
      ? tokenBalances
          .filter((token) => token.tokenAmount.uiAmount && token.mint !== SOLANA_TOKEN_ADDRESS)
          .map((item) => item.mint)
          .slice(0, 50)
      : []
  );
  // const usdBalance = useUsdBalance();

  const [chainId, setChainId] = useState<EChainId>(
    isMainnet ? EChainId.Solana : EChainId.SolanaDevnet
  );

  const currentBalanceInfo = tokenBalances?.find(
    (tokenData) => tokenData.mint === SOLANA_TOKEN_ADDRESS
  );

  const totalBalance = useMemo(() => {
    const totalUseMeme = tokenWithPriceList
      ? tokenWithPriceList.reduce((acc, token) => {
          const price = new BigNumber(token.solPrice || 0);
          const amount = new BigNumber(
            tokenBalances?.find((tokenB) => tokenB.mint === token.tokenAddress)?.tokenAmount
              .uiAmount || 0
          );
          return acc.plus(price.multipliedBy(amount).multipliedBy(new BigNumber(priceUsd || 0)));
        }, new BigNumber(0))
      : new BigNumber(0);
    const nativeBalance = currentBalanceInfo
      ? new BigNumber(currentBalanceInfo.tokenAmount.uiAmount || 0).multipliedBy(
          new BigNumber(priceUsd || 0)
        )
      : new BigNumber(0);
    return totalUseMeme.plus(nativeBalance).toNumber();
  }, [tokenWithPriceList, currentBalanceInfo, tokenBalances, priceUsd]);

  // const functionList = [
  //   {
  //     label: 'Send',
  //     icon: <SendIcon />,
  //     path: `${routePaths.send}?step=${EStep.ENTER_ADDRESS}`,
  //     disabled: true,
  //   },
  //   {
  //     label: 'Deposit',
  //     icon: <DepositIcon />,
  //     path: `${routePaths.deposit}`,
  //     disabled: true,
  //   },
  //   {
  //     label: 'Swap',
  //     icon: <SwapIcon />,
  //     path: `${routePaths.swap}?fromWallet=true`,
  //   },
  //   // {
  //   //   label: 'Staking',
  //   //   icon: <StakingIcon />,
  //   //   path: `${routePaths.staking}`,
  //   // },
  // ];

  // const connectorSelected = () => {
  //   if ([EChainId.Solana, EChainId.SolanaDevnet].includes(chainId)) {
  //     return 'solana';
  //   }
  //   if ([EChainId.Ton, EChainId.TonTestnet].includes(chainId)) {
  //     return 'ton';
  //   }
  //   if ([EChainId.Ethereum, EChainId.Polygon, EChainId.Amoy, EChainId.Sepolia].includes(chainId)) {
  //     return 'eth';
  //   }
  //   return undefined;
  // };

  const totalBalanceDisplay = useMemo(() => {
    if (!Number(totalBalance) || new BigNumber(totalBalance).gte(new BigNumber(1)))
      return formatNumberWithCommas(totalBalance || 0, 2);

    return formatNumberWithSubscriptZeros(totalBalance.toString(), 2);
  }, [totalBalance]);

  return (
    <>
      {/* <div className="flex flex-col items-center mb-6 gap-[14px]"> */}
      <div className="flex flex-col items-center sm:mb-[18px] sm:pb-5 mb-5 gap-[14px]">
        {/* <ConnectorSelect
          initialValue={connectorSelected()}
          onChange={(value) => {
            if (value === 'solana') {
              setChainId(isMainnet ? EChainId.Solana : EChainId.SolanaDevnet);
              return;
            }
            if (value === 'ton') {
              setChainId(isMainnet ? EChainId.Ton : EChainId.TonTestnet);
              return;
            }

            if (value === 'eth') {
              setChainId(isMainnet ? EChainId.Ethereum : EChainId.Sepolia);
              return;
            }

            setChainId(ALL_CHAIN_VALUE);
          }}
        /> */}
        <div className="flex items-center h-[54px]">
          <div className="flex items-end gap-1.5">
            <span
              className={clsx(
                'text-[28px] leading-none font-medium text-gray-800 flex items-end',
                isLoadingTokenPrice && 'animate-pulse'
              )}
            >
              {/* ${formatNumberWithCommas(totalBalance || 0)} */}
              {isLoadingTokenPrice ? '$???' : <>${totalBalanceDisplay}</>}
              {/* {get(user, 'sp_point', '0')} */}
            </span>
            {/* <span className="text-2xl font-medium text-gray-800 flex items-end">PT</span> */}
          </div>
        </div>
      </div>
      <div className="flex flex-col grow">
        {/* <div className="flex gap-4 sm:mb-[18px] sm:pb-5 mb-5"> */}
        {/* {functionList.map((item) => (
            <IconButton
              key={item.label}
              icon={
                <div className="size-12 flex items-center justify-center bg-primary-500/10 rounded-full active icon">
                  {item.icon}
                </div>
              }
              label={item.label}
              onClick={() => {
                router.push(item.path);
              }}
              disabled={item.disabled}
              customClassName="flex-col min-h-fit"
            />
          ))} */}
        {/* <IconButton
            icon={
              <div className="size-5 flex items-center justify-center">
                <SendIcon />
              </div>
            }
            label={'Send'}
            onClick={() => {
              router.push(`${routePaths.send}?step=${EStep.ENTER_ADDRESS}`);
            }}
            disabled
            customClassName="flex-col min-h-fit py-3"
          />
          <IconButton
            icon={
              <div className="size-5 flex items-center justify-center">
                <DepositIcon />
              </div>
            }
            label={'Deposit'}
            onClick={() => {
              router.push(`${routePaths.deposit}`);
            }}
            disabled
            customClassName="flex-col min-h-fit py-3"
          />
          {/* <IconButton
            icon={<StakingIcon />}
            label={'Staking'}
            onClick={() => {
              router.push(`${routePaths.staking}`);
            }}
            customClassName="flex-col min-h-fit py-3"
          /> */}
        {/* </div> */}
        <div className={'grow bg-white rounded-[20px] p-5 flex flex-col gap-3'}>
          {/* <TokenListing
            chainId={chainId}
            setChainId={setChainId}
            onClickItem={(token: Token) =>
              router.push(`${routePaths.token}/${token.address}-${token.chainId}`)
            }
          /> */}
          <Segmented
            // contentClassName={'mt-4'}
            tabs={[
              {
                label: 'Hold',
                content: (
                  <TokenListing
                    chainId={chainId}
                    setChainId={setChainId}
                    onClickItem={(token: Token) =>
                      router.push(`${routePaths.token}/${token.address}-${token.chainId}`)
                    }
                  />
                ),
              },
              {
                label: 'History',
                content: <HistoryTrx />,
              },
            ]}
          />
        </div>
      </div>
    </>
  );
};

export const IconButton = ({
  icon,
  label,
  onClick,
  disabled,
  customClassName,
}: {
  icon: ReactNode;
  label: string;
  onClick: () => void;
  disabled?: boolean;
  customClassName?: string;
}) => {
  return (
    <button
      onClick={onClick}
      className={clsx(
        'flex items-center justify-center gap-2 flex-1',
        disabled && 'cursor-not-allowed opacity-50',
        customClassName
      )}
      disabled={disabled}
    >
      {icon}
      <span className={'font-medium text-sm text-[#666]'}>{label}</span>
    </button>
  );
};
