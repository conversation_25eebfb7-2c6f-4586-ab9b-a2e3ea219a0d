import DatabaseIcon from '@/public/icons/database.svg';
import { Controller, useFormContext } from 'react-hook-form';
import { EventAirdropSchemaFormType } from '../schema';
import clsx from 'clsx';
import FormField from '@/components/shared/FormField';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Checkbox from '@/components/shared/Checkbox';
import { Poppins } from 'next/font/google';
import { GasFeePaymentMethod } from '../../common';
import { createElement, useRef, useState } from 'react';
import Image from 'next/image';
import LoadingSpin from '@/components/shared/LoadingSpin';
import { PlusIcon } from '@radix-ui/react-icons';
import { FILE_TYPE_ARR, MAX_IMAGES } from '@/constants/memePad';
import { toast } from 'react-toastify';
import { AuthService } from '@/services/auth';
import axios from 'axios';
import { SOCIAL_LINKS } from '@/constants/common';
import Button from '@/components/shared/Button';
import React from 'react';
const poppins = Poppins({
  style: ['normal', 'italic'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin', 'latin-ext'],
});

export default function EventDetailStep() {
  const { control, watch, setValue } = useFormContext<EventAirdropSchemaFormType>();
  const [descriptionImages, setDescriptionImages] = useState<string[]>(
    watch('descriptionImages') || []
  );
  const [isUploadingDescriptionImage, setIsUploadingDescriptionImage] = useState<boolean>(false);
  const [isOpenSocialLink, setIsOpenSocialLink] = useState(false);

  const descriptionFileInputRef = useRef<HTMLInputElement | null>(null);
  const socials = watch('socials');
  const triggerDescriptionFileInput = () => {
    if (descriptionFileInputRef.current) {
      descriptionFileInputRef.current.click();
    }
  };
  const handleDescriptionImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsUploadingDescriptionImage(true);
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    try {
      const files = Array.from(event.target.files);
      const remainingSlots = MAX_IMAGES - descriptionImages.length;

      if (files.length > remainingSlots) {
        toast.warning(`You can only upload ${remainingSlots} more image(s)`);
        files.splice(remainingSlots);
      }

      // Check file types and sizes
      for (const file of files) {
        if (!file.type.startsWith('image/') || !FILE_TYPE_ARR.includes(file.type)) {
          toast.error(`${file.name} is not an image file`);
          return;
        }
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`${file.name} exceeds 10MB limit`);
          return;
        }
      }

      // Generate unique filenames
      const fileNames = files.map(
        (file) => crypto.randomUUID() + file.name.substring(file.name.lastIndexOf('.'))
      );

      // Get presigned URLs from server
      const response = await AuthService.generatePresignedUrl({
        fileNames: fileNames,
      });

      // Upload files and collect URLs
      const uploadedUrls: string[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const presignedUrl = response[i];

        await axios.put(presignedUrl.presignedUrl, file, {
          headers: {
            'Content-Type': file.type,
          },
        });

        const imageUrl = presignedUrl.presignedUrl
          .split('?')[0]
          .replace(
            process.env.NEXT_PUBLIC_BUCKET_DOMAIN!,
            process.env.NEXT_PUBLIC_CLOUDFRONT_DOMAIN!
          );

        uploadedUrls.push(imageUrl);
      }

      const updatedImages = [...descriptionImages, ...uploadedUrls];
      setDescriptionImages(updatedImages);
      setValue('descriptionImages', updatedImages, { shouldValidate: true });

      toast.success(`${files.length} image(s) uploaded successfully`);
    } catch {
      toast.error('Failed to upload images');
    } finally {
      setIsUploadingDescriptionImage(false);
    }

    // Reset the input value to allow uploading the same files again
    event.target.value = '';
  };
  const removeDescriptionImage = (index: number) => {
    const updatedImages = [...descriptionImages];
    updatedImages.splice(index, 1);
    setDescriptionImages(updatedImages);
    setValue('descriptionImages', updatedImages, { shouldValidate: true });
  };

  const toggleSocialSelection = (key: string) => {
    if (!socials) return;
    const index = socials.findIndex((f) => f.name === key);
    if (index >= 0) {
      const updatedSocials = [...socials];
      updatedSocials.splice(index, 1);
      setValue('socials', updatedSocials, { shouldValidate: true });
    } else {
      setValue(`socials.${socials.length}`, { name: key, url: '' });
    }
  };

  return (
    <div className="w-full rounded-std bg-white py-6 px-5 flex flex-col gap-6">
      <div className="w-full flex items-center gap-[18px]">
        <div className="rounded-full bg-primary-500/10 flex items-center justify-center size-12">
          <DatabaseIcon />
        </div>
        <span className="text-gray-800 font-medium text-xl">Event Details</span>
      </div>
      <Controller
        name="eventName"
        control={control}
        render={({ field, fieldState }) => (
          <FormField label="Event Name" limit={13}>
            <div
              className={clsx(
                'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                fieldState.error ? 'border-red-500' : 'border-gray-300'
              )}
            >
              <input
                {...field}
                placeholder="Name"
                className="w-full bg-transparent focus:outline-none placeholder:text-gray-600"
                onChange={(e) => field.onChange(e)}
                maxLength={13}
              />
            </div>
            {fieldState.error && (
              <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
            )}
          </FormField>
        )}
      />
      <Controller
        name="eventStartedAt"
        control={control}
        render={({ field, fieldState }) => (
          <FormField label="Event Period">
            <div
              className={clsx(
                'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                fieldState.error ? 'border-red-500' : 'border-gray-300'
              )}
            >
              <DatePicker
                selected={field.value}
                onChange={(date) => field.onChange(date)}
                className="w-full bg-transparent focus:outline-none placeholder:text-gray-600"
                wrapperClassName="w-full"
                placeholderText="MM/DD/YYYY"
                popperPlacement="bottom"
                calendarClassName={poppins.className}
              />
            </div>
            {fieldState.error && (
              <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
            )}
          </FormField>
        )}
      />
      <Controller
        name="gasFeePayment"
        control={control}
        render={({ field, fieldState }) => (
          <FormField label="Gas fee Payment" className="gap-2.5">
            <Checkbox
              containerClassName="gap-5 items-center"
              value={GasFeePaymentMethod.IN_APP_CLAIM}
              checked={field.value === GasFeePaymentMethod.IN_APP_CLAIM}
              onChange={() => field.onChange(GasFeePaymentMethod.IN_APP_CLAIM)}
              label={
                <div className="flex flex-col">
                  <span className="font-medium text-gray-800">In-App Claim</span>
                  <span className="text-xs text-gray-600">Recipients can claim via the app</span>
                  <span className="py-1 px-2.5 text-primary-500 bg-primary-500/10 rounded-lg">
                    Points (equal to the total airdrop) will be added to your account
                  </span>
                </div>
              }
            />
            <Checkbox
              containerClassName="gap-5 items-center"
              value={GasFeePaymentMethod.WALLET_AIRDROP}
              checked={field.value === GasFeePaymentMethod.WALLET_AIRDROP}
              onChange={() => field.onChange(GasFeePaymentMethod.WALLET_AIRDROP)}
              label={
                <div className="flex flex-col">
                  <span className="font-medium text-gray-800">Wallet Airdrop</span>
                  <span className="text-xs text-gray-600">Sent directly to wallet address</span>
                </div>
              }
            />
            {fieldState.error && (
              <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
            )}
          </FormField>
        )}
      />
      <div className="flex flex-col gap-3">
        <Controller
          name="description"
          control={control}
          render={({ field, fieldState }) => (
            <FormField label="Description" limit={100}>
              <div
                className={clsx(
                  'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                  fieldState.error ? 'border-red-500' : 'border-gray-300'
                )}
              >
                <textarea
                  {...field}
                  placeholder="Summary of event description"
                  maxLength={100}
                  className="w-full bg-transparent focus:outline-none placeholder:text-gray-600 resize-none"
                  onChange={(e) => field.onChange(e)}
                />
              </div>
              {fieldState.error && (
                <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
              )}
            </FormField>
          )}
        />
        <div className="flex flex-col gap-[10px] mb-6 std:mb-0 w-full">
          <div className="flex flex-wrap gap-[10px]">
            {descriptionImages.map((imageUrl, index) => (
              <div key={'desc-img-' + index} className="relative group">
                <Image
                  src={imageUrl}
                  alt={`Description image ${index + 1}`}
                  width={160}
                  height={160}
                  className="rounded-[12px] w-[72px] h-[72px] std:w-[82px] std:h-[82px] object-cover"
                />
                <button
                  type="button"
                  onClick={() => removeDescriptionImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
              </div>
            ))}

            {descriptionImages.length < MAX_IMAGES && (
              <div
                onClick={triggerDescriptionFileInput}
                className="rounded-[12px] w-[72px] h-[72px] std:w-[82px] std:h-[82px] bg-gray-300 border border-[#F5F5F5] flex items-center justify-center cursor-pointer hover:bg-[#E5E5E5] transition-colors"
              >
                {isUploadingDescriptionImage ? <LoadingSpin /> : <PlusIcon />}
                <input
                  ref={descriptionFileInputRef}
                  type="file"
                  accept="image/jpeg, image/png, image/gif, image/webp"
                  multiple
                  className="hidden"
                  onChange={handleDescriptionImageUpload}
                  aria-label="Upload description images"
                  disabled={isUploadingDescriptionImage}
                />
              </div>
            )}
          </div>
          <div className="text-xs text-[#666666]">
            <p className="leading-[24px]">Upload image or gif up to 10 MB, 400 × 400</p>
            <p className="leading-[24px]">Maximum of 4 images</p>
          </div>
        </div>
      </div>
      {socials?.map((_, index) => (
        <Controller
          name={`socials.${index}.url`}
          control={control}
          key={index}
          render={({ field, fieldState }) => {
            const socialItem = SOCIAL_LINKS.find((item) => item.key === socials[index].name);
            return (
              <div className="flex flex-col gap-2.5">
                <FormField
                  label={
                    <div className="flex items-center gap-2">
                      {createElement(socialItem?.icon)}
                      {socialItem?.name}
                    </div>
                  }
                  optional
                >
                  <div
                    className={clsx(
                      'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                      fieldState.error ? 'border-red-500' : 'border-gray-300'
                    )}
                  >
                    <input
                      placeholder="Enter the URL"
                      className="w-full bg-transparent focus:outline-none placeholder:text-gray-600"
                      onChange={(e) => field.onChange(e)}
                    />
                  </div>
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </FormField>
                <p className="text-[#666666] text-xs font-normal">
                  {index === 0
                    ? 'This link will be set as a task when claiming the token.'
                    : 'The link you set initially will be shown as a task when claiming the token.'}
                </p>
              </div>
            );
          }}
        />
      ))}
      <div
        className={`flex ${isOpenSocialLink ? 'items-start' : 'items-center'} justify-between gap-[10px] w-full`}
      >
        <div
          onClick={() => setIsOpenSocialLink((prev) => !prev)}
          className="min-w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer"
        >
          <PlusIcon />
        </div>
        {isOpenSocialLink ? (
          <div className="flex flex-wrap gap-2">
            {SOCIAL_LINKS.map((link) => (
              <div
                key={link.key}
                onClick={() => toggleSocialSelection(link.key)}
                className={clsx(
                  'flex items-center justify-center gap-2.5 rounded-[14px] py-2 px-[14px] text-sm font-medium cursor-pointer h-8',
                  socials?.map((s) => s.name).includes(link.key)
                    ? 'bg-primary-500/10 border border-primary-500/30 text-primary-500 icon active'
                    : 'bg-gray-200 text-gray-800'
                )}
              >
                {createElement(link.icon)}
                <span className="text-xs">{link.name}</span>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex justify-between items-center w-full">
            <div className="text-[#666666] text-base font-normal ml-4">Social</div>
            <div className="text-[#666666] text-xs font-normal ml-auto">Optional</div>
          </div>
        )}
      </div>
      <Button variant="primary" className="w-full mt-4">
        Continue
      </Button>
    </div>
  );
}
