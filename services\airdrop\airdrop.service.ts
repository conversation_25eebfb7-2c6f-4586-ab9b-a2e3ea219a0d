import { Api } from '@/lib/axios';
import { IListResponse } from '../meme-launchpad';
import {
  IAirdrop,
  IAirdropRankConfig,
  ICreatorAirdrop,
  ICreatorAirdropTotalByToken,
  IListAirdropParams,
  IListEventAirdropParams,
  ISolAirdrop,
  ISolPrice,
  ISummaryPoint,
  ITotalAirdropInfo,
} from './airdrop.types';
import { IPagination, IParam } from '@/types/common';

const BASE_URL = process.env.NEXT_PUBLIC_MEME_API_URL;

export const AirdropServices = {
  getAirdropList: (params: IListAirdropParams): Promise<IListResponse<IAirdrop>> => {
    return Api.get(BASE_URL + '/coin/list/airdrop', { params });
  },
  getAirdropDetail: (tokenAddress: string): Promise<IAirdrop> => {
    return Api.get(BASE_URL + `/coin/airdrop/${tokenAddress}`);
  },
  putUpdateStatusAirdrop: (id: number) => {
    return Api.put(BASE_URL + `/coin/airdrop/status/${id}`);
  },
  postClaimAirdrop: (id: number) => {
    return Api.post(`/user/airdrop/token/claim/${id}`);
  },
  getSolAirdropList: (params: IListAirdropParams): Promise<IPagination<ISolAirdrop>> => {
    return Api.get('/user/airdrop/sol', { params });
  },
  getSolPriceAirdrop: (id: string[]): Promise<ISolPrice[]> => {
    return Api.get(`/user/airdrop/list-sol-price`, { params: { id } });
  },
  postClaimSolAirdrop: (id: number) => {
    return Api.post(`/user/airdrop/sol/claim/${id}`);
  },
  getSummaryPoint: (): Promise<ISummaryPoint> => {
    return Api.get('/user/airdrop/swap');
  },
  getClaimedCreatorAirdrop: (params: IListAirdropParams): Promise<IPagination<ICreatorAirdrop>> => {
    return Api.get('/user/airdrop/creator', { params });
  },
  getTotalAirdropInfo: (): Promise<ITotalAirdropInfo> => {
    return Api.get('/user/fee/info');
  },
  getAirdropRankConfig: (): Promise<IAirdropRankConfig[]> => {
    return Api.get('/ranking/config');
  },
  getCreatorAirdropTotalByToken: (
    params: IParam
  ): Promise<IPagination<ICreatorAirdropTotalByToken>> => {
    return Api.get('/user/airdrop/creator/group-by-token', { params });
  },
  //Event airdrop
  getEventAirdrop: (params: IListEventAirdropParams): Promise<IPagination<ISolAirdrop>> => {
    return Api.get('/user/airdrop/sol', { params });
  },
};
