'use client';
import Image from 'next/image';
import Button from '@/components/shared/Button';
import { useAuth } from '@/hooks/use-auth';
import { formatNumberWithCommas } from '@/utils/common';
import Link from 'next/link';
import RankIcon from '../shared/RankIcon';
import { useMemo, useState } from 'react';
import clsx from 'clsx';
import { useIsFetching } from '@tanstack/react-query';
import { EQueryKeys } from '@/services/query-keys';
import RandomAvatar from '../account/RandomAvatar';
import { useGetSummaryAirdrop } from '@/services/meme-launchpad';
import { round } from 'lodash';
import { useGetAirdropRankConfig, useGetSummaryPoint } from '@/services/airdrop';

import SwapIcon from '@/public/icons/24x24-menu-icon/swap-icon.svg';
import IncreaseIcon from '@/public/icons/increace-icon.svg';
import IncreaseIcon2 from '@/public/icons/increace-icon-2.svg';
import TriangleDownIcon from '@/public/icons/triangle-arrow-down.svg';
// import GachaIcon from '@/public/icons/24x24-menu-icon/gacha-icon.svg';
// import ImageIcon from '@/public/icons/24x24-menu-icon/image-icon.svg';
import OutLineInfoIcon from '@/public/icons/outline-info-icon.svg';
import Rank from '@/public/icons/rank.svg';
// import { ESubTask, ETabs } from '../Get/type';
import { routePaths } from '@/constants/common';
// import TaskEventCard from '@/features/homepage/TaskEventCard';
// import { useGetAllGatchaConfig } from '@/services/gatcha/gatcha.queries';
import { range } from 'lodash';
import {
  INewUserPointInfo,
  useGetNewUserPointInfo,
  useGetTotalUser,
  useGetUserRank,
} from '@/services/auth';
// import DraggableDiv from '../shared/DraggableDiv';
// import { useMintItem } from '@/services/nft';
// import socket from '@/app/socket';
// import { toast } from 'react-toastify';

// import CompletedIcon from '@/public/icons/complete-task-icon.svg';
// import LoadingSpin from '../shared/LoadingSpin';
import { Cross2Icon } from '@radix-ui/react-icons';
import { useIsClient, useLocalStorage } from 'usehooks-ts';
import { EStorageKeys } from '@/lib/storage';
import { MdArrowForwardIos } from 'react-icons/md';
import PopUpModal from '../shared/PopUpModal';
import { useDisclosure } from '@/hooks/use-disclosure';
import CustomToolTip from '../shared/CustomTooltip';
import SideMenu from '@/features/layout/SideMenu';
// import { EWalletTabs } from '@/features/wallet';
// import { EPointSubTabs } from '@/features/wallet/points/points-tab';
import NumberDisplay from '../shared/NumberDisplay';
// import AirdropIcon from '@/public/images/airdrop-thumb.png';
// import { useRouter } from 'next/navigation';
import { EAirdropTab } from '../airdrop/common';

const FULL_UPDATE_INFO_THRESHOLD = new Date(Date.UTC(2025, 6, 1, 0, 0, 0)); // 1 July 2025, 00:00:00 UTC+0

const updateInfoList = [
  {
    banner: '/component/update-info-banner/step-1.png',
    title: 'Transaction Fee Distribution',
    description: (
      <ul className="list-disc pl-6 leading-6">
        <li>
          Creator : <span className="font-medium">1.5%</span>
        </li>
        <li>
          Platform : <span className="font-medium">1%</span>
        </li>
        <li>
          Ranking Airdrop : <span className="font-medium">0.5%</span>
        </li>
      </ul>
    ),
  },
  {
    banner: '/component/update-info-banner/step-2.png',
    title: 'User Rewards Details',
    description: (
      <ul className="list-disc pl-6 leading-6">
        <li>
          <span className="font-medium">Creator Reward</span>
          <p className="text-xs leading-6">
            1.5% of transaction fees go to token creators every time their tokens are traded.
          </p>
        </li>
        <li>
          <span className="font-medium">Daily Rank Reward</span>
          <p className="text-xs leading-6">
            0.5% of the transaction fees are airdropped daily to eligible S-C users based on their
            ranking.
          </p>
        </li>
      </ul>
    ),
  },
  {
    banner: '/component/update-info-banner/step-3.png',
    title: 'Monthly Point Ranking',
    description: (
      <p className="leading-6 text-xs">
        Your Rank(S~C) will be determined based on your position in this monthly ranking.
        <br />
        You can earn points by trading tokens or creating your own token, and these points will be
        reflected in the monthly Point Ranking.
        <br />
        <ul className="list-disc pl-6 leading-6">
          <li>Trade $1 to earn 1 point(PT)</li>
          <li>Mint a token to get 100PT</li>
        </ul>
        The top 30,000 users are eligible for the Ranks AirDrops. Earn more points to get more
        AirDrops!
      </p>
    ),
  },
  {
    banner: '/component/update-info-banner/step-4.png',
    title: 'Point Conversion Rate',
    description: (
      <p className="text-sm leading-6">
        Any remaining rockets and points in your account will automatically be used for Gacha. New
        points will be awarded based on the number and attributes of your items.
        <br />
        <br />
        <span className="font-medium">Past items will be converted as follows :</span>
        <br />
        <div className="flex">
          1 item →&nbsp;
          <p>
            Normal: 50 PT
            <br />
            Rare: 100 PT
            <br />
            Super Rare: 150 PT
          </p>
        </div>
      </p>
    ),
  },
  {
    banner: '/component/update-info-banner/step-5.png',
    title: 'Rocket Purchase Refunds',
    description: (
      <p className="leading-6">
        Users who purchased Rockets will receive refunds.
        <br />
        Each 1 TON has been converted at a rate of 0.0242 SOL and refunded in SOL.
        <br />
        Refunds have already been processed — please check your wallet.
      </p>
    ),
  },
  {
    banner: '/component/update-info-banner/step-6.png',
    title: 'New Smart Pocket is here!',
    description: (
      <p className="text-center leading-6">
        We are here for everyone.
        <br />
        Explore new features, earn more rewards, and enjoy the all-new Smart Pocket!
      </p>
    ),
  },
];

const InfoContent = ({
  close,
  newUserPointInfo,
  isUserCreatedAfter,
}: {
  close: () => void;
  newUserPointInfo: INewUserPointInfo;
  isUserCreatedAfter: boolean;
}) => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);

  const renderInfoList = useMemo(() => {
    const userCreatedAt = new Date(user!.created_at);
    if (userCreatedAt?.getTime() > FULL_UPDATE_INFO_THRESHOLD.getTime()) {
      return updateInfoList.slice(0, 3);
    }

    if (isUserCreatedAfter) {
      return updateInfoList;
    }

    const totalGachaItem =
      (newUserPointInfo?.count_score_0 || 0) +
      (newUserPointInfo?.count_score_1 || 0) +
      (newUserPointInfo?.count_score_2 || 0);

    const lastInfo = {
      banner: '',
      title: '',
      description: (
        <div className="w-full flex flex-col gap-3 items-center">
          <div className="flex flex-col gap-6 items-center">
            <Image
              src="/icons/completed-icon.svg"
              className="size-[95px]"
              width={95}
              height={95}
              alt="complete image"
            />
            <span className="text-xl font-medium text-primary-500 leading-none">
              {`Your New Point : ${newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.total_point) : '???,???'} PT`}
            </span>
          </div>
          <div className="flex flex-col gap-1 w-full items-center">
            <p className="text-center text-gray-800 text-xs leading-[18px]">
              {`Old Point ${
                newUserPointInfo
                  ? formatNumberWithCommas((totalGachaItem - newUserPointInfo.old_rocket) * 1000)
                  : '???,???'
              } PT → ${
                newUserPointInfo
                  ? formatNumberWithCommas(totalGachaItem - newUserPointInfo.old_rocket)
                  : '???,???'
              } Item`}
            </p>
            <p className="text-center text-gray-800 text-xs leading-[18px]">
              {`Old Rocket ${newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.old_rocket) : '???,???'} Rocket → ${newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.old_rocket) : '???,???'} Item`}
            </p>
            <span className="font-medium">
              {`Total : ${newUserPointInfo ? formatNumberWithCommas(totalGachaItem) : '???,???'} Item`}
            </span>

            <p
              className={
                'w-full text-xs input-text-gray-600-black py-1.5 px-3 bg-gray-300 rounded-lg text-center leading-6'
              }
            >
              {`Normal : ${newUserPointInfo ? newUserPointInfo.count_score_0 : '???'} Item → ${newUserPointInfo ? newUserPointInfo.point_score_0 : '???'} PT`}
              <br />
              {`Rare 1 : ${newUserPointInfo ? newUserPointInfo.count_score_1 : '???'} Item →
              ${newUserPointInfo ? newUserPointInfo.point_score_1 : '???'} PT`}
              <br />
              {`Rare 2 : ${newUserPointInfo ? newUserPointInfo.count_score_2 : '???'} Item →
              ${newUserPointInfo ? newUserPointInfo.point_score_2 : '???'} PT`}
            </p>
          </div>
        </div>
      ),
    };
    return [...updateInfoList, lastInfo];
  }, [newUserPointInfo, isUserCreatedAfter, user]);

  const { banner, title, description } = renderInfoList[currentStep];

  const isEnd = currentStep === renderInfoList.length - 1;

  return (
    <div className="flex flex-col gap-3 grow">
      <div className="min-w-[calc(100%+4px)] pr-1 flex flex-col items-center max-h-[calc(80dvh-88px)] sm:max-h-[calc(100dvh-104px-48px-103px)] overflow-auto">
        {banner && (
          <Image
            className="aspect-[345/190] w-[345px]"
            src={banner}
            alt="banner"
            width={345}
            height={190}
          />
        )}
        <div className="w-full flex flex-col gap-1 items-center text-gray-800">
          {title && <span className="text-lg font-medium">{title}</span>}
          {description && <div className="text-sm w-full">{description}</div>}
        </div>
      </div>
      <div className="flex flex-col gap-6 w-full items-center justify-end grow">
        <div className="flex gap-1">
          {range(renderInfoList.length).map((index) => (
            <span
              key={'indicator' + index}
              className={clsx(
                'h-1 rounded-full cursor-pointer transition-all ease-in-out',
                index === currentStep ? 'w-[19px] bg-primary-500' : 'w-2.5 bg-gray-300'
              )}
              onClick={() => setCurrentStep(index)}
            ></span>
          ))}
        </div>
        <Button
          className="!w-full"
          onClick={() => {
            if (isEnd) {
              close();
            } else setCurrentStep(currentStep + 1);
          }}
        >
          {isEnd ? 'Close' : 'Next'}
        </Button>
      </div>
    </div>
  );
};

export default function NewHomeAccount() {
  const { user, isAuthenticated } = useAuth();
  // const router = useRouter();
  const { data: userRanking, isLoading: isLoadingUserRanking } = useGetUserRank(isAuthenticated);
  const { data: totalUser, isLoading: isLoadingTotalUser } = useGetTotalUser();
  const { data: newUserPointInfo, isLoading: isLoadingNewUserPointInfo } =
    useGetNewUserPointInfo(isAuthenticated);

  const updateInfoModal = useDisclosure();
  const sideMenu = useDisclosure();

  const [isShow, setIsShow] = useLocalStorage(EStorageKeys.rocket_info_banner, false);
  const [isShowNewPointInfo, setIsShowNewPointInfo] = useLocalStorage(
    EStorageKeys.new_point_info_banner,
    false
  );
  const isClient = useIsClient();

  const isFetchingProfile = useIsFetching({
    queryKey: [EQueryKeys.PROFILE],
  });

  const { data: summaryAirdrop, isLoading: isLoadingSummaryAirdrop } =
    useGetSummaryAirdrop(isAuthenticated);
  const { data: summaryPoint, isLoading: isLoadingSummaryPoint } =
    useGetSummaryPoint(isAuthenticated);

  const totalAllTimeAirdrop = useMemo(() => {
    if (!summaryAirdrop) return '???,???';

    const total =
      summaryAirdrop.solAirdropAmount.allTime +
      summaryAirdrop.swapMyToken.allTime +
      summaryAirdrop.swapAirdrop.allTime;

    return total;
  }, [summaryAirdrop]);

  const totalTodayAirdrop = useMemo(() => {
    if (!summaryAirdrop) return '???,???';

    const total =
      summaryAirdrop.solAirdropAmount.today +
      summaryAirdrop.swapMyToken.today +
      summaryAirdrop.swapAirdrop.today;

    return total;
  }, [summaryAirdrop]);

  const { data: airdropRankConfig } = useGetAirdropRankConfig();
  const getAirdropPercentage = (rankName: string) => {
    return airdropRankConfig?.find((item) => item.rank_name === rankName)?.air_drop_percentage || 0;
  };
  const airdropRatio = useMemo(() => {
    return [
      {
        rank: 'S',
        value: getAirdropPercentage('S'),
        className: 'bg-primary-500',
      },
      {
        rank: 'A',
        value: getAirdropPercentage('A'),
        className: 'bg-[#f6c101]',
      },
      {
        rank: 'B',
        value: getAirdropPercentage('B'),
        className: 'bg-[#aeaeae]',
      },
      {
        rank: 'C',
        value: getAirdropPercentage('C'),
        className: 'bg-[#e9954d]',
      },
    ];
  }, [airdropRankConfig]);

  const totalGachaItem = useMemo(() => {
    return (
      (newUserPointInfo?.count_score_0 || 0) +
      (newUserPointInfo?.count_score_1 || 0) +
      (newUserPointInfo?.count_score_2 || 0)
    );
  }, [newUserPointInfo]);

  const isUserCreatedAfter = useMemo(() => {
    if (!user || !user.created_at) return false;
    const createdAt = new Date(user.created_at);
    // 26 June 2025, 12:00:00 UTC+0
    const threshold = new Date(Date.UTC(2025, 5, 26, 12, 0, 0));
    return createdAt.getTime() > threshold.getTime();
  }, [user]);

  const currentRankConfig = useMemo(() => {
    if (!userRanking?.rankName)
      return {
        rank: '?',
        value: 0,
        className: 'bg-[#989898]',
      };

    const config = airdropRatio.find((config) => config.rank === userRanking?.rankName);

    if (!config) {
      return {
        rank: '?',
        value: 0,
        className: 'bg-[#989898]',
      };
    }
    return config;
  }, [userRanking, airdropRatio]);

  return (
    <div className="w-full max-w-full flex flex-col gap-[14px]">
      {/* <div className="bg-primary-gradient rounded-[20px] p-3.5 flex gap-3 items-center">
        <Image
          width={60}
          height={60}
          className="size-[60px]"
          src={AirdropIcon}
          alt="airdrop icon"
        />
        <div className="flex flex-col gap-3 text-white">
          <span className="font-medium text-lg">$SP AirDrop Coming Soon!!</span>
          <Button
            variant="outline"
            className="hover:bg-white !px-4 !py-1.5 !text-xs rounded-[22px]"
            onClick={() => router.push(routePaths.airdropConfirm)}
          >
            Check Your Airdrop <MdArrowForwardIos />
          </Button>
        </div>
      </div>
      <div
        className="rounded-[20px] p-3.5 flex gap-3 items-center"
        style={{ background: 'linear-gradient(291.5deg, #B30469 0%, #F29317 100%)' }}
      >
        <Image
          width={60}
          height={60}
          className="size-[60px]"
          src={AirdropIcon}
          alt="airdrop icon"
        />
        <div className="flex flex-col gap-3 text-white">
          <span className="font-medium text-lg">AirDrop Checker : Investor</span>
          <Button
            variant="outline"
            className="hover:bg-white !px-4 !py-1.5 !text-xs rounded-[22px]"
            onClick={() => router.push(`${routePaths.airdropInvestor}`)}
          >
            Check Your Airdrop <MdArrowForwardIos />
          </Button>
        </div>
      </div> */}
      <div className="flex flex-col w-full bg-white rounded-std pt-6 pb-2.5 px-[18px] gap-5">
        <div
          onClick={() => sideMenu.open()}
          className="flex justify-between items-center cursor-pointer"
        >
          <div className="w-full flex items-center gap-[18px] max-w-[calc(100%-20px-18px)]">
            {user?.display_icon ? (
              <Image
                src={user?.display_icon}
                alt="avatar"
                width={56}
                height={56}
                className="size-14 object-cover rounded-full border border-gray-200"
              />
            ) : (
              <RandomAvatar className="size-14 object-cover rounded-full border border-gray-200" />
            )}
            <div className="flex items-center gap-2.5 max-w-[calc(100%-56px-18px)]">
              <span
                className={clsx(
                  'text-lg font-medium text-gray-800 truncate',
                  isFetchingProfile && 'animate-pulse'
                )}
              >
                {user?.display_name || user?.uuid || '?????????'}
              </span>
            </div>
          </div>
          <RankIcon rank={userRanking?.rankName} width={20} height={20} />
        </div>
        <div className="flex flex-col gap-1.5">
          <div className="flex flex-col items-end gap-3">
            <div className="w-full flex flex-col gap-1 bg-gray-300 py-4 px-5 rounded-2xl">
              <span className="text-gray-600 text-sm font-medium">Total AirDrop</span>
              <div className="w-full flex justify-between">
                <span
                  className={clsx(
                    'text-gray-800 text-xl font-medium',
                    isLoadingSummaryAirdrop && 'animate-pulse'
                  )}
                >
                  $<NumberDisplay number={totalAllTimeAirdrop} />
                </span>
                <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium">
                  Today :&nbsp;
                  <span
                    className={clsx('text-green-600', isLoadingSummaryAirdrop && 'animate-pulse')}
                  >
                    +$
                    <NumberDisplay number={totalTodayAirdrop} />
                  </span>
                </span>
              </div>
            </div>
            <div className="w-full flex items-center justify-between px-2">
              <div className="flex items-center gap-1">
                <Rank />{' '}
                <span className="font-medium text-sm text-gray-800">
                  Your Rank : {userRanking?.rankName}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-800">Airdrop Requirements</span>
                <CustomToolTip
                  className="!max-w-64"
                  title="Airdrop Requirements"
                  content={
                    <p className="list-disc">
                      <li>
                        The airdrop will be distributed to the top 30,000 users based on point
                        rankings.
                      </li>
                      <li>There are four ranks: C, B, A, and S.</li>
                      <li>
                        Airdrop conditions are subject to change and may be adjusted periodically
                        without prior notice.
                      </li>
                      <li>Airdrop value will be calculated in USD at the time of distribution.</li>
                    </p>
                  }
                >
                  <OutLineInfoIcon className="cursor-pointer" />
                </CustomToolTip>
              </div>
            </div>
          </div>
          <div className="w-full flex flex-col">
            <Link
              href={routePaths.airdrop + `?tab=${EAirdropTab.CREATOR}`}
              className="w-full flex items-center py-4 gap-[18px]"
            >
              <span className="size-12 rounded-full bg-primary-500/10 flex items-center justify-center icon active">
                <SwapIcon />
              </span>
              <span className="text-gray-900 font-medium grow">Total Creator Fee</span>
              <div className="flex items-center gap-2.5">
                <div className="flex flex-col items-end gap-2">
                  <span
                    className={clsx(
                      'text-gray-800 font-medium leading-none',
                      isLoadingSummaryAirdrop && 'animate-pulse'
                    )}
                  >
                    $<NumberDisplay number={summaryAirdrop?.swapMyToken.allTime || 0} />
                  </span>
                  <span className="text-gray-600 text-sm leading-4 whitespace-nowrap">
                    Today :&nbsp;
                    <span
                      className={clsx('text-green-600', isLoadingSummaryAirdrop && 'animate-pulse')}
                    >
                      +<NumberDisplay number={summaryAirdrop?.swapMyToken.today || 0} />
                    </span>
                  </span>
                </div>
                <MdArrowForwardIos size={14} color="#999" />
              </div>
            </Link>
            <Link
              href={routePaths.airdrop + `?tab=${EAirdropTab.RANK}`}
              className="w-full flex items-center py-4 gap-[18px]"
            >
              <span className="size-12 rounded-full bg-primary-500/10 flex items-center justify-center icon active">
                <SwapIcon />
              </span>
              <span className="text-gray-900 font-medium grow">Total Rank Fee</span>
              <div className="flex items-center gap-2.5">
                <div className="flex flex-col items-end gap-2">
                  <span
                    className={clsx(
                      'text-gray-800 font-medium leading-none',
                      isLoadingSummaryAirdrop && 'animate-pulse'
                    )}
                  >
                    $
                    <NumberDisplay number={summaryAirdrop?.swapAirdrop.allTime || 0} />
                  </span>
                  <span className="text-gray-600 text-sm leading-4 whitespace-nowrap">
                    Today :&nbsp;
                    <span
                      className={clsx('text-green-600', isLoadingSummaryAirdrop && 'animate-pulse')}
                    >
                      +<NumberDisplay number={summaryAirdrop?.swapAirdrop.today || 0} />
                    </span>
                  </span>
                </div>
                <MdArrowForwardIos size={14} color="#999" />
              </div>
            </Link>
          </div>
        </div>
      </div>
      <div className="w-full bg-white px-[18px] py-5 rounded-std flex flex-col gap-5">
        <div className="flex items-center text-primary-500 font-semibold text-xs">
          <div className="relative bg-[#EE811A1A] w-fit px-[15px] py-3 rounded-l-3xl flex items-center justify-center">
            Get Point
            <div
              className="absolute z-10 -right-3 w-[35px] h-full bg-white"
              style={{ clipPath: 'polygon(60% 0, 85% 50%, 60% 100%, 35% 100%, 60% 50%, 35% 0)' }}
            />
          </div>
          <div className="relative bg-[#EE811A1A] grow pl-[19px] pr-[14px] py-3 inline-flex items-center justify-center">
            Ranking & Rank <IncreaseIcon />
            <div
              className="absolute -right-[10px] w-[35px] h-full bg-white"
              style={{ clipPath: 'polygon(60% 0, 85% 50%, 60% 100%, 35% 100%, 60% 50%, 35% 0)' }}
            />
          </div>
          <div className="bg-[#EE811A1A] w-fit pr-[10px] pl-3 py-3 rounded-r-3xl flex items-center justify-center">
            AirDrop <IncreaseIcon />
          </div>
        </div>
        <div className="flex flex-col gap-3.5">
          <div className="w-full flex flex-col bg-gray-300 py-4 px-5 rounded-2xl">
            <span className="text-gray-600 text-sm font-medium">Monthly Total Points</span>
            <div className="w-full flex justify-between">
              <span
                className={clsx(
                  'text-gray-800 text-xl font-medium',
                  isLoadingSummaryPoint && 'animate-pulse'
                )}
              >
                {summaryPoint ? formatNumberWithCommas(round(summaryPoint.point)) : '???,???'}
                &nbsp;<span className="text-base">PT</span>
              </span>
              <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium whitespace-nowrap">
                Today :{' '}
                <span className={clsx('text-green-600', isLoadingSummaryPoint && 'animate-pulse')}>
                  +{summaryPoint ? <NumberDisplay number={summaryPoint.today_point} /> : '???,???'}
                </span>
              </span>
            </div>
          </div>
          <div className="flex items-center justify-center icon-gray-200">
            <TriangleDownIcon />
          </div>
          <div className="w-full flex flex-col gap-2.5">
            <div className="w-full flex items-center justify-between">
              <span className="text-gray-900 font-medium">Monthly Ranking</span>
              <span className="text-xs text-gray-800">Airdrop to Top 30,000</span>
            </div>
            <div className="flex flex-col gap-[14px] w-full">
              <div className="flex items-center h-6 relative">
                <div
                  className={clsx(
                    'w-full bg-gray-300 h-6 overflow-hidden rounded-lg flex items-center justify-between',
                    (isLoadingUserRanking || isLoadingTotalUser) && 'animate-pulse'
                  )}
                >
                  {!userRanking?.rankName && (
                    <span className="text-xs leading-6 input-text-gray-600-black h-6 pl-2.5">
                      No rank
                    </span>
                  )}
                  <div
                    className={clsx(
                      'h-6 max-w-full flex justify-center items-center text-xs text-white rounded-lg font-medium px-2 min-w-6',
                      currentRankConfig.className
                    )}
                    style={{
                      width: userRanking?.rankName
                        ? `${userRanking?.ranking && totalUser ? Math.max(round((totalUser - userRanking.ranking, totalUser) / totalUser) * 100, 1) : 100}%`
                        : 'fit-content',
                    }}
                  >
                    {currentRankConfig.rank}
                  </div>
                </div>
              </div>
              <div
                className={clsx(
                  'w-full py-[14px] px-5 bg-gray-300 rounded-xl text-gray-600 font-medium flex items-center justify-between gap-3',
                  isLoadingTotalUser && 'animate-pulse'
                )}
              >
                <div className="flex items-center gap-2">
                  <span className="text-gray-800 leading-[17px] font-medium">
                    {userRanking
                      ? formatNumberWithCommas(userRanking?.ranking || '???,???')
                      : '???,???'}
                  </span>
                  <span className="text-sm leading-[14px]">
                    / {totalUser ? formatNumberWithCommas(totalUser || '???,???') : '???,???'}
                  </span>
                </div>
                <span className="rounded-full flex py-1.5 px-4 bg-white text-gray-800 text-xs font-medium">
                  Today :&nbsp;
                  <span
                    className={clsx(
                      userRanking?.rankingChange! < 0 && '[&>svg>path]:fill-red-800 rotate-180',
                      userRanking?.rankingChange! > 0 && '[&>svg>path]:fill-green-600'
                    )}
                  >
                    <IncreaseIcon2 />
                  </span>
                  <span
                    className={clsx(
                      userRanking?.rankingChange! > 0 && 'text-green-600',
                      userRanking?.rankingChange! < 0 && 'text-red-800',
                      isLoadingUserRanking && 'animate-pulse'
                    )}
                  >
                    {userRanking?.rankingChange || 0}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center  icon-gray-200">
            <TriangleDownIcon />
          </div>
          <div className="flex flex-col gap-2.5">
            <div className="flex items-center justify-between">
              <span className="text-gray-900 font-medium">AirDrop</span>
              <span className="text-xs font-medium text-gray-800">
                {userRanking?.rankName} Rank Airdrop to Holders:{' '}
                {userRanking?.rankName
                  ? airdropRatio.find((item) => item.rank === userRanking?.rankName)?.value
                  : 0}
                %
              </span>
            </div>
            <div className="flex w-full">
              {airdropRatio.map((item) => (
                <div
                  key={item.rank}
                  className={clsx(
                    'rounded-lg text-white text-xs leading-none font-medium flex justify-center py-1.5',
                    item.className,
                    item.rank !== userRanking?.rankName && 'opacity-30'
                  )}
                  style={{ width: `${item.value}%`, minWidth: '24px' }}
                >
                  {item.rank}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      {!isShow && isClient && (
        <div className="relative w-full flex items-center gap-3 p-[14px] rounded-std bg-white">
          <Cross2Icon
            className="text-gray-600 absolute right-4 top-4 cursor-pointer"
            onClick={() => setIsShow(true)}
          />
          <Image
            src="/images/rocket-fund.png"
            className="size-[72px] aspect-square"
            width={72}
            height={72}
            alt="rocket refund"
          />
          <div className="flex flex-col gap-1.5">
            <div className="flex flex-col gap-1 text-gray-800">
              <span className="font-medium text-sm">New Smart Pocket Update</span>
              <p className="text-xs">
                Specification changes due to the start of user rewards
                <br />
                (Point adjustments & Rocket refunds)
              </p>
            </div>
            <span
              className="text-xs font-medium text-primary-500 flex gap-2 items-center cursor-pointer"
              onClick={() => updateInfoModal.open()}
            >
              More View
              <MdArrowForwardIos size={12} className="fill-primary-500" />
            </span>
          </div>
        </div>
      )}
      {!isShowNewPointInfo && isClient && !isUserCreatedAfter && (
        <div className="relative w-full flex gap-3 p-[14px] bg-white rounded-std">
          <Cross2Icon
            className="text-gray-600 absolute right-4 top-4 cursor-pointer"
            onClick={() => setIsShowNewPointInfo(true)}
          />
          <Image
            src="/icons/completed-icon.svg"
            width={40}
            height={40}
            alt="icon"
            className="size-10"
          />
          <div className="flex flex-col gap-1">
            <span
              className={clsx(
                'text-sm text-primary-500 font-medium',
                isLoadingNewUserPointInfo && 'animate-pulse'
              )}
            >
              {`Your New Point : ${newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.total_point) : '???,???'} PT`}
            </span>
            <p
              className={clsx(
                'text-xs input-text-gray-600-black',
                isLoadingNewUserPointInfo && 'animate-pulse'
              )}
            >
              {`Old Point ${
                newUserPointInfo
                  ? formatNumberWithCommas((totalGachaItem - newUserPointInfo.old_rocket) * 1000)
                  : '???,???'
              } PT → ${
                newUserPointInfo
                  ? formatNumberWithCommas(totalGachaItem - newUserPointInfo.old_rocket)
                  : '???,???'
              } Item`}
              <br />
              {`Old Rocket ${newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.old_rocket) : '???,???'} Rocket → ${
                newUserPointInfo ? formatNumberWithCommas(newUserPointInfo.old_rocket) : '???,???'
              } Item`}
              <br />
              <span className="font-medium">
                {`Total : ${newUserPointInfo ? formatNumberWithCommas(totalGachaItem) : '???,???'} Item`}
              </span>
            </p>
            <p
              className={clsx(
                'text-xs input-text-gray-600-black py-1.5 px-3 bg-gray-300 rounded-lg',
                isLoadingNewUserPointInfo && 'animate-pulse'
              )}
            >
              {`Normal : ${newUserPointInfo ? newUserPointInfo.count_score_0 : '???'} Item → ${newUserPointInfo ? newUserPointInfo.point_score_0 : '???'} PT`}
              <br />
              {`Rare 1 : ${newUserPointInfo ? newUserPointInfo.count_score_1 : '???'} Item → ${newUserPointInfo ? newUserPointInfo.point_score_1 : '???'} PT`}
              <br />
              {`Rare 2 : ${newUserPointInfo ? newUserPointInfo.count_score_2 : '???'} Item → ${newUserPointInfo ? newUserPointInfo.point_score_2 : '???'} PT`}
            </p>
          </div>
        </div>
      )}
      {/* <div className="flex flex-col w-full bg-white rounded-std pt-5 pb-2.5 px-[18px] gap-2.5">
        <div className="w-full flex flex-col bg-gray-300 py-4 px-5 rounded-2xl">
          <span className="text-gray-600 text-sm font-medium">Monthly Total Points</span>
          <div className="w-full flex justify-between">
            <span
              className={clsx(
                'text-gray-800 text-xl font-medium',
                isLoadingSummaryPoint && 'animate-pulse'
              )}
            >
              {summaryPoint ? formatNumberWithCommas(round(summaryPoint.point)) : '???,???'}
              &nbsp;<span className="text-base">PT</span>
            </span>
            <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium whitespace-nowrap">
              Today :{' '}
              <span className={clsx('text-green-600', isLoadingSummaryPoint && 'animate-pulse')}>
                +{summaryPoint ? <NumberDisplay number={summaryPoint.today_point} /> : '???,???'}
              </span>
            </span>
          </div>
        </div>
        <div className="w-full flex flex-col">
          <Link className="w-full flex items-center py-4 gap-[18px]" href={routePaths.swap}>
            <span className="size-12 rounded-full bg-primary-500/10 flex items-center justify-center icon active">
              <SwapIcon />
            </span>
            <span className="text-gray-900 font-medium grow">Swap</span>
            <div className="flex items-center gap-2.5">
              <div className="flex flex-col items-end gap-2">
                <span
                  className={clsx(
                    'text-gray-800 font-medium leading-none',
                    isLoadingSummaryPoint && 'animate-pulse'
                  )}
                >
                  {summaryPoint
                    ? formatNumberWithCommas(round(summaryPoint.swap_point), 0)
                    : '???,???'}
                  &nbsp;PT
                </span>
                <span className="text-gray-600 text-sm leading-4 whitespace-nowrap">
                  Today :{' '}
                  <span
                    className={clsx('text-green-600', isLoadingSummaryPoint && 'animate-pulse')}
                  >
                    +{summaryPoint ? <NumberDisplay number={summaryPoint.today_point} /> : '???'}
                  </span>
                </span>
              </div>
              <MdArrowForwardIos size={14} color="#999" />
            </div>
          </Link>
          <Link
            className="w-full flex items-center py-4 gap-[18px]"
            href={`${routePaths.wallet}?tab=${EWalletTabs.POINTS}&subTab=${EPointSubTabs.HISTORY}`}
          >
            <span className="size-12 rounded-full bg-primary-500/10 flex items-center justify-center">
              <GachaIcon className="icon active" />
            </span>
            <span className="text-gray-900 font-medium grow">Giveaway</span>
            <div className="flex items-center gap-2.5">
              <div className="flex flex-col items-end gap-2">
                <span
                  className={clsx(
                    'text-gray-800 font-medium leading-none',
                    isLoadingSummaryPoint && 'animate-pulse'
                  )}
                >
                  {`${formatNumberWithCommas(0)} PT`}
                </span>
                <span className="text-gray-600 text-sm leading-4 whitespace-nowrap">
                  Today :{' '}
                  <span
                    className={clsx('text-green-600', isLoadingSummaryPoint && 'animate-pulse')}
                  >{`+${formatNumberWithCommas(0)}`}</span>
                </span>
              </div>
              <MdArrowForwardIos size={14} color="#999" />
            </div>
          </Link>
          <Link
            className="w-full flex items-center py-4 gap-[18px]"
            href={`${routePaths.wallet}?tab=${EWalletTabs.POINTS}&subTab=${EPointSubTabs.HISTORY}`}
          >
            <span className="size-12 rounded-full bg-primary-500/10 flex items-center justify-center">
              <ImageIcon />
            </span>
            <span className="text-gray-900 font-medium grow">OG</span>
            <div className="flex items-center gap-2.5">
              <div className="flex flex-col items-end gap-2">
                <span
                  className={clsx(
                    'text-gray-800 font-medium leading-none',
                    isLoadingSummaryPoint && 'animate-pulse'
                  )}
                >
                  {`${summaryPoint?.old_point ? formatNumberWithCommas(round(summaryPoint?.old_point || 0)) : '???'} PT`}
                </span>
                <span className="text-gray-600 text-sm leading-4">Today : 0</span>
              </div>
              <MdArrowForwardIos size={14} color="#999" />
            </div>
          </Link>
        </div>
      </div> */}
      <SideMenu
        isOpen={sideMenu.opened}
        setIsOpen={(value) => {
          if (value) {
            sideMenu.open();
          } else {
            sideMenu.close();
          }
        }}
        isNativeApp
      />
      <PopUpModal
        isOpen={updateInfoModal.opened}
        onClose={updateInfoModal.close}
        className="py-6 max-sm:h-[580px] h-[592px] max-h-[80dvh]"
      >
        <InfoContent
          close={updateInfoModal.close}
          newUserPointInfo={newUserPointInfo!}
          isUserCreatedAfter={isUserCreatedAfter}
        />
      </PopUpModal>
    </div>
  );
}
