import { ICoin } from '../meme-launchpad';

export enum EAirdropStatus {
  SOON = 'SOON',
  CLAIM = 'CLAIM',
  CHECK = 'CHECK',
  TOTAL = 'TOTAL',
}

export enum EEventAirdropStatus {
  CLAIM = 'CLAIM',
  SOON = 'SOON',
  DONE = 'DONE',
}

export interface IListAirdropParams {
  page?: number;
  limit?: number;
  airdropStatusFilter?: EAirdropStatus;
}

export interface IListEventAirdropParams {
  page?: number;
  limit?: number;
  airdropStatusFilter?: EEventAirdropStatus;
}
export interface IUpdateStatusAirdropPayload {
  status: EAirdropStatus;
}

export interface IAirdrop {
  name: string;
  tokenAddress: string;
  logoUri: string;
  symbol: string;
  bondingCurve: number;
  airdropStatus: EAirdropStatus;
  airdropSnapshot: {
    id: number;
    user_id: number;
    rank: string;
    token_address: string;
    amount: number;
    status: EAirdropStatus;
    claimed_at: string | null;
    created_at: string;
    updated_at: string;
  };
  isClaimable: boolean;
}

export interface ISolAirdrop {
  id: number;
  user_id: number;
  rank: string;
  usd_amount: number;
  sol_amount: number;
  claimed_at: string | null;
  transfer_hash: string | null;
  swap_hash: string | null;
  status: EAirdropStatus;
  created_at: string;
  updated_at: string | null;
}

export interface ICreatorAirdrop {
  id: number;
  user_id: number;
  usd_amount: number;
  sol_amount: number;
  token_address: string;
  hash: string;
  created_at: string;
  updated_at: string;
}

export interface ISolPriceParams {
  id: string[];
}

export interface ISolPrice extends ICoin {
  solPrice: number;
}

export interface ISummaryPoint {
  id: number;
  user_id: number;
  point: number;
  today_point: number;
  swap_point: number;
  old_point: number;
  created_at: string;
}

export interface ITotalAirdropInfo {
  totalRankFee: number;
  totalRankFeeToday: number;
  totalCreatorFee: number;
  totalCreatorFeeToday: number;
  total: number;
}

export interface IAirdropRankConfig {
  id: number;
  rank_name: string;
  percentage: number;
  air_drop_percentage: number;
}

export interface ICreatorAirdropTotalByToken {
  token_address: string;
  total_sol: number;
}
