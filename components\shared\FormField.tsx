import clsx from 'clsx';
import { ReactNode } from 'react';

interface FormFieldProps {
  label?: string | ReactNode;
  limit?: number;
  optional?: boolean;
  children: ReactNode;
  className?: string;
}

const FormField = ({ label, limit, optional, children, className }: FormFieldProps) => {
  return (
    <div className="flex flex-col gap-[10px]">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <label className="text-[#666666] text-base font-normal">{label}</label>
        </div>
        <span className="text-[#666666] text-xs font-normal">
          {limit ? limit : optional ? 'Optional' : ''}
        </span>
      </div>
      <div className={clsx('flex flex-col gap-1', className)}>{children}</div>
    </div>
  );
};

export default FormField;
