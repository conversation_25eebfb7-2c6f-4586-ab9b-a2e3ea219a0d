@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  ul,
  ol {
    list-style: revert;
  }
}

html {
  max-height: 100dvh;
  overflow: hidden;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    Fira Sans,
    Droid Sans,
    Helvetica Neue,
    sans-serif;
  background: #f5f5f5;
  width: 100%;
  height: 100%;
  overflow: hidden;
  overscroll-behavior: none;
}

@media screen and (max-width: 660px) {
  body {
    background: #f5f5f5;
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

body:has(.body-bg-white) {
  background: #fff;
}

body:has(.gacha-page, .invite-code-page) .menu-pc {
  pointer-events: auto;
}

body:has(.login-page) .mobile-menu-bar {
  display: none;
}

.active,
.icon:hover {
  path:not(.telegram_svg__fixed-color, .website_svg__fixed-color) {
    fill: #ee811a;
  }
}

.active,
.icon:hover {
  path.website_svg__stroke-color-icon {
    stroke: #ee811a;
  }
}

.gray-icon {
  path {
    fill: #666;
  }
}
.icon-gray-200{
  path {
    fill:#F0EEEE;
  }
}

.default-bg {
  background-image: url('/images/smart-pocket-default-image.png');
  background-position: center;
  background-size: cover;
}
/* 
.swiper-wrapper {
  align-items: stretch;
} */
.swiper-button-prev,
.swiper-button-next {
  color: #999 !important;
  transform: scale(0.6);
}
.swiper-pagination-fraction {
  color: #ee811a !important;
  height: fit-content;
  width: fit-content !important;
  transform: translateX(-50%) !important;
  left: 50% !important;
  top: -40px !important;
  padding: 6px 20px;
  background: linear-gradient(0deg, rgba(238, 129, 26, 0.1), rgba(238, 129, 26, 0.1)), #ffffff;
  border: 1px solid #ee811a;
  border-radius: 30px;
  font-size: 12px;
  line-height: 1;
}
.swiper:has(.swiper-pagination) {
  overflow: visible;
  overflow-x: clip;
}

/* .swiper-pagination {
  bottom: unset !important;
  height: 4px;
  .swiper-pagination-bullet {
    width: 10px;
    height: 4px;
    border-radius: 2px;
    background: #e5e5e5 !important;
    opacity: 1 !important;
  }
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 19px;
    background: #ee811a !important;
  }
} */

.main-content-layout:has(.mint-site) {
  background-image: url('/images/mint-site-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
}

.main-content-layout:has(.coming-soon-page) {
  padding-bottom: 0px !important;
}

.main-content-layout:has(.main-content-no-top-padding) {
  padding-top: 0 !important;
}

@media screen and (max-width: 1295px) {
  body:has(.header-tabs) .center-panel {
    padding-top: 114px !important;
    .main-content-layout {
      max-height: calc(100dvh - 114px) !important;
    }
  }
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.h-screen {
  height: 100dvh;
}

.max-h-screen {
  max-height: 100dvh;
}

.min-h-screen {
  min-height: 100dvh;
}

#qr-scanner-container {
  position: relative;
  width: max-content;
  height: max-content;
  overflow: hidden;
}
#qr-scanner-container .scan-region-highlight {
  border-radius: 20px;
  outline: rgba(0, 0, 0, 0.7) solid 50vmax;
}
#qr-scanner-container .scan-region-highlight-svg {
  display: none;
}
#qr-scanner-container .code-outline-highlight {
  stroke: rgba(255, 255, 255, 0.5) !important;
  stroke-width: 15 !important;
  stroke-dasharray: none !important;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  scrollbar-width: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.main-content-layout:has(.no-layout) {
  padding: 0;
  @media screen and (max-width: 919px) {
    padding-bottom: 76px;
  }
}

.center-panel:has(.no-layout) {
  @media screen and (max-width: 919px) {
    padding-inline: 0;
  }
}

input[type='range'] {
  -webkit-appearance: none;
  appearance: none;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  margin-top: -4.5px;
  border: 3px solid #ffffff;
  height: 22px;
  width: 22px;
  border-radius: 11px;
  background: #ee811a;
  cursor: pointer;
}

/* All the same stuff for Firefox */
input[type='range']::-moz-range-thumb {
  -webkit-appearance: none;
  appearance: none;
  border: 3px solid #ffffff;
  height: 22px;
  width: 22px;
  border-radius: 11px;
  background: #ee811a;
  cursor: pointer;
}

/* All the same stuff for IE */
input[type='range']::-ms-thumb {
  -webkit-appearance: none;
  appearance: none;
  border: 3px solid #ffffff;
  height: 22px;
  width: 22px;
  border-radius: 11px;
  background: #ee811a;
  cursor: pointer;
}

input[type='range']::-webkit-slider-runnable-track {
  width: 100%;
  height: 12px;
  cursor: pointer;
  /* background: #d9d9d9; */
  border-radius: 6px;
}

input[type='range']::-ms-fill-upper {
  background: #ee811a;
}

input[type='range']:focus {
  outline: none;
}

.no-select {
  user-select: none;
  -webkit-user-select: none; /* Chrome, Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
}

.input-text-gray-600-black {
  color: rgba(0, 0, 0, 0.2);
  text-shadow: 0 0 0 #999999;
}

.input-text-gray-600-black::placeholder {
  color: rgba(0, 0, 0, 0.2);
  text-shadow: 0 0 0 #999999;
}

body:has(.children-only) .pc-side-menu,
body:has(.children-only) .mobile-menu-bar,
body:has(.children-only) .pc-header,
body:has(.children-only) .mobile-header,
body:has(.children-only) #right-panel {
  display: none !important;
}

.custom-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  @apply w-6 h-6 border-2 rounded-[4px] cursor-pointer;

  @apply bg-white;
  border-color: #b9bbbd;
}

.custom-checkbox:checked {
  @apply border-primary-500 bg-white;

  position: relative;
}

.custom-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 7px;
  width: 6px;
  height: 12px;
  border: solid #f97316;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.token-slide {
  .swiper-pagination:not(.swiper-pagination-lock) {
    display: flex;
    justify-content: center;
    margin-top: 12px;
    position: unset !important;
  }
  .swiper-pagination-bullet {
    width: 10px;
    height: 4px;
    background: #e5e5e5;
    opacity: 1;
    margin: 0 4px;
    border-radius: 2px;
  }
  .swiper-pagination-bullet-active {
    width: 19px;
    border-radius: 2px;
    background: #ee811a;
  }
  .swiper-wrapper {
    z-index: 30;
  }
  .token-launcher-btn {
    z-index: 40;
  }
}
@media screen and (min-width: 920px) {
  .content-section:has(.login-page) {
    align-items: center !important;
    justify-content: center !important;
  }
}

@media screen and (max-width: 920px) {
  .mobile-header:has(.search-input-opened) .header-title {
    position: absolute;
  }
}

body:has(.search-input-opened) .user-menu-trigger {
  display: none;
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.animate-side-menu[data-state='closed'] {
  animation: slideOut 0.3s ease-in-out forwards;
}

body:has(.mobile-trade-button-wrapper) .mobile-menu-bar {
  display: none;
}

.alpha-list {
  list-style: none;
  counter-reset: item;
  padding-left: 1.25rem;
}

.alpha-list li {
  counter-increment: item;
  position: relative;
  margin-bottom: 0.5em;
}

.alpha-list li::before {
  font-size: 12px;
  content: '(' counter(item, lower-alpha) ') ';
  position: absolute;
  left: -1.75em;
}

.roman-list {
  list-style: none;
  counter-reset: roman;
  padding-left: 1.25rem;
}

.roman-list li {
  counter-increment: roman;
  position: relative;
  margin-bottom: 0.5em;
}

.roman-list li::before {
  font-size: 12px;
  content: '(' counter(roman, lower-roman) ') ';
  position: absolute;
  left: -2.25em;
}

#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}
#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}

.animate-zoom {
  animation: zoom-in-zoom-out 3s ease-in-out infinite;
}

@keyframes zoom-in-zoom-out {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(.95);
  }
  100% {
    transform: scale(1);
  }
}

.react-datepicker{
  color:#666666;
  .react-datepicker__header {
    background-color: #EE811A1A;
    .react-datepicker__current-month{
      color:#ee811a
    }
  }
  .react-datepicker__navigation-icon{
    font-size:inherit!important;
    &::before{
      border-color: #ee811a!important;
    }
  }
}