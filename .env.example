NEXT_PUBLIC_ALCHEMY_KEY=
NEXT_PUBLIC_HOST=
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_MS_CLARITY_ID=
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=
NEXT_PUBLIC_W3A_CLIENT_ID=
NEXT_PUBLIC_API_URL=http://localhost:3300/api/v1
NEXT_PUBLIC_MEME_API_URL=http://localhost:3300/api/v1/meme
NEXT_PUBLIC_MEME_WS_DOMAIN=http://localhost:3300
NEXT_PUBLIC_SERVER_DOMAIN=https://api.dev-smartpocke.sotatek.works
NEXT_PUBLIC_GG_CLIENT_ID=
NEXT_PUBLIC_GG_API_KEY=
NEXT_PUBLIC_W3A_GG_VERIFIER=sp-gg-verifier
NEXT_PUBLIC_W3A_TELEGRAM_VERIFIER=sp-telegram-verifier
# App network (mainnet, testnet)
NEXT_PUBLIC_APP_NETWORK=testnet
NEXT_PUBLIC_BUCKET_DOMAIN=
NEXT_PUBLIC_CLOUDFRONT_DOMAIN=
NEXT_PUBLIC_JUPITER_API_KEY=
NEXT_PUBLIC_QUICK_NODE_URL=
NEXT_PUBLIC_QUICK_NODE_DEV_URL=

# Outer link
NEXT_PUBLIC_DISCORD_URL=https://discord.gg/smartpocket
NEXT_PUBLIC_X_URL=https://x.com/smapocke
NEXT_PUBLIC_TERM_URL=https://smapocke.com/TermsofUse
NEXT_PUBLIC_PRIVACY_URL=https://smapocke.com/PrivacyPolicy
NEXT_PUBLIC_MARKETPLACE_EXPLORE_URL=https://uat.smartpocket-nft.sotatek.works/explore/all
NEXT_PUBLIC_MARKETPLACE_COLLECTIONS_URL=https://uat.smartpocket-nft.sotatek.works/choose-type
NEXT_PUBLIC_MARKETPLACE_CREATE_NFT_URL=https://uat.smartpocket-nft.sotatek.works/collections
NEXT_PUBLIC_MARKETPLACE_URL=https://uat.smartpocket-nft.sotatek.works
NEXT_PUBLIC_MARKETPLACE_SEARCH_LINK=https://smapocke-marketplace.app/search
NEXT_PUBLIC_TELEGRAM_SHARE_URL=https://telegram.me/share/url?url=
NEXT_PUBLIC_X_SHARE_URL=https://telegram.me/share/url?url=
NEXT_PUBLIC_TELEGRAM_LOGIN=https://t.me/izana_sp_bot?start=
NEXT_PUBLIC_FIRST_TASK_LINK="https://x.com/pockemys"
NEXT_PUBLIC_FIRST_TASK_SBT_ID="19"
NEXT_PUBLIC_SP_OFFICIAL_SITE_URL=https://sp-token.com/
#1752598800000=16/07/2025 00:00:00 UTC+0
NEXT_PUBLIC_BONDING_CURVE_UPDATE_AT=1752624000000

#Maintenance mode true/false
NEXT_PUBLIC_MAINTENANCE_MODE=false
NEXT_PUBLIC_RELEASE_BANNER_TOKEN_ADDRESS=
