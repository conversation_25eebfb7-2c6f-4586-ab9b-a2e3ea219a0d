import { FileInputField, InputField, LoadingScreen } from '@/components/core';
import EmptyData from '@/components/core/empty-data';
import { InputRange } from '@/components/core/input-range';
import { SelectField } from '@/components/core/select-field';
import { Button, Checkbox, Input } from '@/components/ui';
import { EAirdropConditionType } from '@/constants/airdrop';
import { parseCsvFile, parseExcelFile, parseTxtFile } from '@/lib/helpers';
import { formatAddress } from '@/lib/utils';
import { AirdropSchemaFormType } from '@/schema/airdrop';
import { usePostAirdropEventHolders } from '@/services/airdrop/airdrop.queries';
import clsx from 'clsx';
import { findLast } from 'lodash';
import { useRef, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

const importTypeOptions = [
  {
    label: 'Token Holder',
    value: EAirdropConditionType.TOKEN_HOLDERS,
  },
  {
    label: 'NFT Holder',
    value: EAirdropConditionType.NFT_HOLDERS,
  },
  {
    label: 'Import files',
    value: EAirdropConditionType.CSV,
  },
  {
    label: 'X Reply Import',
    value: EAirdropConditionType.X_REPLY,
  },
];

const headers = [
  {
    label: 'Select',
    value: 'select',
  },
  {
    label: 'Address',
    value: 'address',
  },
];

const getInputInfoWithType = (type: EAirdropConditionType) => {
  switch (type) {
    case EAirdropConditionType.X_REPLY:
      return 'X Post URL';
    default:
      return 'Contract Address';
  }
};

const getArrFromFile = (file: File) => {
  const type = findLast(file.name.split('.'));

  switch (type) {
    case 'csv':
      return parseCsvFile(file);
    case 'txt':
      return parseTxtFile(file);
    case 'xlsx':
      return parseExcelFile(file);
  }
};

export default function ImportAirdropAddressSection() {
  const [addresses, setAddresses] = useState<{ address: string }[]>();
  const { mutateAsync: postAirdropEventHolders, isPending: isLoadingAddress } =
    usePostAirdropEventHolders();
  const { watch, setValue, getValues } = useFormContext<AirdropSchemaFormType>();
  const { control } = useFormContext<AirdropSchemaFormType>();
  console.log(getValues());
  const { airdropCondition } = getValues();
  const selectedAddress = watch('airdropAddress');
  const selectedAddressCount = selectedAddress?.length;
  const inputAddressRef = useRef<HTMLInputElement>(null);
  const currentImportType = watch('airdropCondition.type') as EAirdropConditionType;

  const handleGetAirdropAddress = async () => {
    const listHolders = await postAirdropEventHolders({
      type: airdropCondition.type as EAirdropConditionType,
      inputData: airdropCondition.data,
    });
    if (listHolders) {
      const addresses = listHolders.map((item) => {
        return { address: item.walletAddress };
      });
      setAddresses(addresses);
    }
  };

  const handleGetAddressFromFile = async (files?: File[]) => {
    if (files) {
      console.log(files);
      const result = await getArrFromFile(files[0]);
      setAddresses(result);
      setValue(
        'airdropAddress',
        result?.map((item) => item.address)
      );

      console.log(result);
    }
  };

  const renderList = () => {
    if (isLoadingAddress) {
      return <LoadingScreen />;
    }
    if (addresses && addresses.length > 0) {
      return (
        <>
          {addresses?.map((item) => {
            const { address } = item;
            const isSelected = selectedAddress?.includes(address);

            return (
              <tr key={address} className="w-full border-b border-gray-3">
                <td className="py-[22px] px-2 min-w-fit text-sm text-gray-5 font-medium flex items-center gap-2">
                  <Checkbox
                    checked={isSelected}
                    className="data-[state=checked]:bg-orange-1 data-[state=checked]:border-orange-1"
                    onClick={() => {
                      if (!isSelected && !selectedAddress?.includes(address)) {
                        console.log('check');

                        setValue('airdropAddress', [
                          ...(selectedAddress ? selectedAddress : []),
                          address,
                        ]);
                      } else if (selectedAddress) {
                        setValue('airdropAddress', [
                          ...selectedAddress.filter((item) => item !== address),
                        ]);
                      }
                    }}
                  />
                </td>

                <td className={clsx('py-[22px] px-2 min-w-fit text-sm text-gray-5 font-medium')}>
                  {formatAddress(address)}
                </td>
              </tr>
            );
          })}
        </>
      );
    }
    return <EmptyData />;
  };

  return (
    <form className="w-full flex flex-col gap-2.5">
      <div className="flex flex-col gap-2">
        <span className="font-semibold text-sm text-gray-5">Airdrop Address</span>
        <Controller
          name="airdropCondition.type"
          control={control}
          render={({ field }) => (
            <SelectField
              value={field.value}
              options={importTypeOptions}
              onChange={(v) => {
                if (v === field.value) {
                  return;
                }
                field.onChange(v as EAirdropConditionType);
                setAddresses(undefined);
              }}
            />
          )}
        />
      </div>
      {[
        EAirdropConditionType.TOKEN_HOLDERS,
        EAirdropConditionType.NFT_HOLDERS,
        EAirdropConditionType.X_REPLY,
      ].includes(currentImportType) && (
        <div className="flex flex-col gap-2">
          <div className="font-semibold text-sm text-gray-5">
            {getInputInfoWithType(currentImportType)}
          </div>
          <InputField
            name="airdropCondition.data"
            ref={inputAddressRef}
            className={clsx(
              'bg-white text-gray-5 text-base rounded-2xl w-full pl-4 pr-3 py-3 focus:outline-none hover:border-orange-1 focus:border-orange-1 placeholder-gray-9'
            )}
            onBlur={(e) => {
              e.target.value = e.target.value.trim();
            }}
          />
        </div>
      )}
      {EAirdropConditionType.CSV === currentImportType && (
        <FileInputField
          data={[]}
          accept=".csv,.txt,.xlsx"
          label="File"
          onChange={(files) => handleGetAddressFromFile(files)}
        />
      )}
      {!addresses ? (
        <Button
          variant={'primary'}
          type="button"
          onClick={() => {
            handleGetAirdropAddress();
          }}
        >
          Import Address
        </Button>
      ) : (
        <>
          <Button
            variant={'outline'}
            type="button"
            onClick={() => {
              handleGetAirdropAddress();
            }}
          >
            Refresh Address
          </Button>
          <div className="w-full flex flex-col gap-3">
            <span className="font-semibold text-sm text-gray-5">Number of Top Addresses</span>
            <div className="w-full flex justify-between items-center py-[9px] px-4 bg-gray-6 rounded-2xl border border-gray-9">
              <Button type="button">-</Button>
              <span className="text-gray-7 font-medium">{selectedAddressCount}</span>
              <Button type="button">+</Button>
            </div>
            <InputRange
              step={1}
              min={1}
              max={addresses?.length}
              value={selectedAddress?.length}
              onChange={(e) => {
                const { value } = e.target as HTMLInputElement;
                console.log({ value });
              }}
            />
          </div>
          <table className="w-full table-auto">
            <thead>
              <tr className="w-full border-b border-gray-3 text-left text-gray-5  whitespace-nowrap">
                {headers.map((header) => (
                  <th key={header.label} className="pb-2.5 px-2 font-semibold">
                    <div className="flex items-center gap-1">{header.label}</div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>{renderList()}</tbody>
          </table>
        </>
      )}
    </form>
  );
}
