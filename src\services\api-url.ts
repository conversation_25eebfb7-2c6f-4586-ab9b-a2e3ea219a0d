export const API_URL = {
  LOGIN: '/auth/admin',
  PRESENT_EVENT: '/admin/event',
  USER_POINT: '/admin/user-point',
  MINT_SITE: '/admin/mint-site',
  POINT_LOG: '/admin/point-log',
  EXCHANGE: '/admin/exchange',
  PFP: '/admin/pfp',
  PFP_CATEGORY: '/admin/pfp-category',
  GACHA: '/admin/gacha',
  GACHA_IMAGE: '/admin/gacha/image',
  MINT_SITE_UPLOAD_FILE: '/admin/mint-site/gen-presigned-url',
  PRESENT_EVENT_UPLOAD_FILE: '/admin/event/generate-presigned-url',
  COLLECTION_UPLOAD_FILE: '/admin/collection/gen-presigned-url',
  RANK_CONFIG: '/admin/ranking-config',
  SHOP: '/admin/rocket',
  COLLAB: '/admin/collab',
  MEME_PAD_WAIT_LIST: '/admin/meme-pad/wait-list',
  POST_X: '/admin/air-drop/config/x',

  BONDING_CURVE_LOG: '/bonding',
  MEME_PAD_ANALYTIC: '/admin/meme-pad/analytic',
  MEME_PAD: '/admin/meme-pad',

  WITHDRAW_PLATFORM_FEE: '/admin/platform-fee',
  SWAP_FEE: '/analytic/transfer-fee',
  TRANSACTIONS: '/analytic/user/list-trades',

  AIRDROP: '/admin/send-token',
  AIRDROP_LIST_SOL_PRICE: '/admin/airdrop/list-sol-price',
  AIRDROP_EVENT_HOLDERS: '/airdrop-event/list-holders',
  AIRDROP_EVENT_CONFIG: '/airdrop-event/config/create',
};

export const memeEndpoint = import.meta.env.VITE_MEME_PAD_API_URL;
export const memePadProdEndpoint = import.meta.env.VITE_PROD_API_URL;
