import { AirdropSchemaFormType } from '@/schema/airdrop';
import dayjs from 'dayjs';
import { useFormContext } from 'react-hook-form';

export default function PreviewAirdrop() {
  const { getValues } = useFormContext<AirdropSchemaFormType>();
  const { airdropAddress, airdropStartAt, airdropQuantity, eventName } = getValues();

  console.log({ v: getValues() });
  return (
    <div className="w-full flex flex-col gap-5">
      <div className="w-full flex justify-between items-center gap-[18px]">
        <div className="rounded-full size-[60px] border-[3px] border-[#f1f0f0]"></div>
        <div className="flex flex-col gap-2.5 grow">
          <span className="text-[#4c4c4c] font-medium">{eventName}</span>
          <span className="py-1 px-2.5 rounded-md bg-orange-1/10 text-orange-1 w-fit">
            Snap Shot : {dayjs(airdropStartAt).format('DD MMM')}
          </span>
        </div>
      </div>
      <div className="w-full flex items-center justify-between text-gray-5">
        <span>AirDrop per Person</span>
        <span>{airdropQuantity || 0}</span>
      </div>
      <div className="w-full flex items-center justify-between text-gray-5">
        <span>Number of Addresses</span>
        <span>{airdropAddress?.length}</span>
      </div>
      <div className="w-full flex items-center justify-between text-gray-5">
        <span>Fee</span>
        <div className="flex flex-col items-end gap-2">
          1 SOL + Gas Fee
          <span className="py-1 px-2 rounded-md text-gray-5 bg-gray-6">
            {airdropAddress?.length} Addresses × 0.001 SOL
          </span>
        </div>
      </div>
      <hr className="w-full border border-gray-9"></hr>
      <div className="w-full flex items-center justify-between text-gray-5 font-medium">
        <span>Total</span>
        <span>{airdropQuantity}</span>
      </div>
    </div>
  );
}
