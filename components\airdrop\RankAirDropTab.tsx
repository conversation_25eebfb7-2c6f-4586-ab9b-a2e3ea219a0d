import BigNumber from 'bignumber.js';
import { getBtnClass, getStatusText, MIN_SOL_CLAIMABLE } from './common';
import {
  EAirdropStatus,
  ISolAirdrop,
  useGetInfiniteSolAirdropList,
  usePostClaimSolAirdrop,
} from '@/services/airdrop';
import FetchDataStatementWrapper from '../shared/FetchDataStatementWrapper';
import AirDropItem from './AirDropItem';
import Button, { BUTTON_SIZE } from '../shared/Button';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import { formatDate } from 'date-fns';
import { formatNumber, getRaydiumDirectLink, openNewTabWithAnchor } from '@/utils/common';
import { routePaths } from '@/constants/common';
import { toast } from 'react-toastify';
import LoadMoreButton from '../shared/LoadMoreButton';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '@/services/query-keys';

interface Props {
  currentFilter: EAirdropStatus;
  onUnClaimableClick: () => void;
}

export default function RankAirDropTab({ currentFilter, onUnClaimableClick }: Props) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const {
    data: solAirdrops,
    hasNextPage: hasNextSolAirdropPage,
    isLoading: isLoadingSolAirdrops,
    isFetching: isFetchingSolAirdrops,
    fetchNextPage: fetchNextSolAirdropPage,
    isRefetching: isRefetchingSolAirdrops,
  } = useGetInfiniteSolAirdropList({ params: { airdropStatusFilter: currentFilter } });
  const { mutateAsync: claimSolAirdrop, isPending: isClaimingSolAirdrop } =
    usePostClaimSolAirdrop();

  const [updatingIds, setUpdatingIds] = useState<Array<number | string>>([]);

  const flattenSolAirdrops = useMemo(() => {
    if (!solAirdrops || !solAirdrops.pages.length) return [];
    return solAirdrops.pages.flatMap((page) => page.data);
  }, [solAirdrops]);

  const redirect = (status: EAirdropStatus, tokenAddress?: string) => {
    if (status === EAirdropStatus.SOON && tokenAddress) {
      router.push(routePaths.memePadDetail(tokenAddress || ''));
      return;
    }

    if (status === EAirdropStatus.CHECK) {
      router.push(routePaths.wallet + `?tab=token`);
      return;
    }

    if (tokenAddress) openNewTabWithAnchor(getRaydiumDirectLink(tokenAddress));
  };

  const handleClaimSolAirdrop = async (airdrop: ISolAirdrop) => {
    const { id, status } = airdrop;
    if (status === EAirdropStatus.CHECK) {
      redirect(status);
      return;
    }

    try {
      setUpdatingIds((prev) => [...prev, id]);
      await claimSolAirdrop(id);
      queryClient.invalidateQueries({ queryKey: [EQueryKeys.SOL_AIRDROP_LIST] });
      queryClient.invalidateQueries({ queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE] });
      toast.success('Successfully claimed!');
    } catch (error) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message
          : 'Error claiming airdrop';
      toast.error(errorMessage || `Claim fail!`);
    } finally {
      setUpdatingIds((prev) => prev.filter((item) => item !== id));
    }
  };

  return (
    <FetchDataStatementWrapper
      isLoading={isLoadingSolAirdrops || isRefetchingSolAirdrops}
      isEmptyData={!flattenSolAirdrops.length}
      customNotDataComponent={
        <AirDropItem
          title="No items found"
          isSoon
          actionButton={
            <Button
              size={BUTTON_SIZE.SMALL}
              className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.SOON))}
              disabled
            >
              {getStatusText(EAirdropStatus.SOON)}
            </Button>
          }
        />
      }
    >
      <div className="w-full max-h-[500px] overflow-auto flex flex-col gap-3">
        {flattenSolAirdrops.map((airdrop) => {
          const { status } = airdrop;
          const isHistory = status === EAirdropStatus.CHECK;
          const isUnClaimable = new BigNumber(airdrop.sol_amount).lte(
            new BigNumber(MIN_SOL_CLAIMABLE)
          );
          return (
            <AirDropItem
              key={airdrop.id}
              progress={100}
              icon="/icons/chain-sol.svg"
              title="Rank Fee"
              subTitle={
                isHistory
                  ? formatDate(airdrop.created_at, 'dd MMM yyyy')
                  : formatNumber(airdrop.sol_amount, 9)
              }
              actionButton={
                isHistory ? (
                  <span className="text-gray-900 font-medium">{`+${formatNumber(airdrop.sol_amount, 9)} SOL`}</span>
                ) : (
                  <div className="relative">
                    <Button
                      size={BUTTON_SIZE.SMALL}
                      className={clsx('rounded-full !px-4', getBtnClass(status))}
                      onClick={() => handleClaimSolAirdrop(airdrop)}
                      isLoading={isClaimingSolAirdrop && updatingIds.includes(airdrop?.id || 0)}
                      disabled={isUnClaimable}
                    >
                      {getStatusText(status)}
                    </Button>
                    {isUnClaimable && (
                      <div
                        className="absolute w-full h-full top-0 left-0"
                        onClick={onUnClaimableClick}
                      ></div>
                    )}
                  </div>
                )
              }
              isHistory={isHistory}
            />
          );
        })}
        <div className="flex justify-center w-full">
          <LoadMoreButton
            hasNextPage={!!hasNextSolAirdropPage}
            isFetching={isFetchingSolAirdrops}
            fetchNextPage={fetchNextSolAirdropPage}
          >
            View More
          </LoadMoreButton>
        </div>
      </div>
    </FetchDataStatementWrapper>
  );
}
