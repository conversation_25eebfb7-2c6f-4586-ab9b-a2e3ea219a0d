import Image from 'next/image';
import Smapocke from '@/public/icons/smapocke_v2.png';
import Button, { BUTTON_SIZE } from '@/components/shared/Button';

type Props = {
  airdropDetail: any;
};
export default function InProgressAirdropEventSection({ airdropDetail }: Props) {
  return (
    <div className="flex flex-col gap-2.5">
      <div className="flex gap-[18px] py-3.5">
        <Image
          src={Smapocke.src}
          alt={'Airdrop Logo'}
          width={48}
          height={48}
          className="rounded-full size-12 object-cover"
        />
        <div className="w-full flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <div className="font-medium text-gray-900 leading-none">
              {airdropDetail.tokenSymbol}
            </div>
            <span className="text-sm text-gray-600 leading-none">
              Available : ??? ??? {airdropDetail.tokenSymbol}
            </span>
          </div>
          <Button size={BUTTON_SIZE.SMALL} className="!px-4" disabled>
            Claim
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-1.5">
        <div className="w-full justify-between flex items-center text-gray-800">
          <span className="text-sm font-medium">Your Airdrop</span>
          <div className="flex items-center">
            <span className="text-sm font-medium">88% / </span>
            <span className="text-xs">100%</span>
          </div>
        </div>
        <div className="w-full flex bg-gray-200 rounded-[20px] h-2">
          <span
            style={{
              width: '88%',
              background: 'linear-gradient(270deg, #FFAB00 -0.2%, #FF4B00 100%)',
            }}
            className=" h-full rounded-[20px]"
          />
        </div>
        <span className="text-xs text-gray-800 text-right">0.0033% per day / 10% per month</span>
      </div>
    </div>
  );
}
