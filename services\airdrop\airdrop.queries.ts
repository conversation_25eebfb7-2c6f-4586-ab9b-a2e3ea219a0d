import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '../query-keys';
import { AirdropServices } from './airdrop.service';
import { IListAirdropParams, IListEventAirdropParams } from './airdrop.types';

export const useGetAirdropList = (params?: IListAirdropParams) => {
  return useQuery({
    queryKey: [EQueryKeys.AIRDROP_LIST, { params }],
    queryFn: () => AirdropServices.getAirdropList({ page: 1, limit: 10, ...params }),
  });
};

export const useGetInfiniteAirdropList = (params?: IListAirdropParams) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.AIRDROP_LIST_INFINITE, ...(params ? Object.entries(params) : [])],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getAirdropList({
        ...params,
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.nextPage;
    },
  });
};

export const useGetSolPriceAirdrop = (id: string[]) => {
  return useQuery({
    queryKey: [EQueryKeys.SOL_PRICE_AIRDROP, id.length],
    queryFn: () => AirdropServices.getSolPriceAirdrop(id),
    enabled: id.length > 0,
  });
};

export const useGetSolAirdropList = (params?: IListAirdropParams) => {
  return useQuery({
    queryKey: [EQueryKeys.SOL_AIRDROP_LIST, ...(params ? Object.entries(params) : [])],
    queryFn: () =>
      AirdropServices.getSolAirdropList({
        page: 1,
        limit: 10,
        ...params,
      }),
  });
};

export const useGetInfiniteSolAirdropList = ({
  params,
  enabled = true,
}: {
  params?: IListAirdropParams;
  enabled?: boolean;
}) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE, ...(params ? Object.entries(params) : [])],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getSolAirdropList({
        ...params,
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
    enabled,
  });
};

export const useGetAirdropDetail = (airdropId: string) => {
  return useQuery({
    queryKey: [EQueryKeys.AIRDROP_DETAIL, airdropId],
    queryFn: () => AirdropServices.getAirdropDetail(airdropId),
    enabled: !!airdropId,
  });
};

export const useUpdateAirdropStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.putUpdateStatusAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.AIRDROP_LIST, EQueryKeys.AIRDROP_LIST_INFINITE],
      });
    },
  });
};

export const usePostClaimAirdrop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.postClaimAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.AIRDROP_LIST, EQueryKeys.AIRDROP_LIST_INFINITE],
      });
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
      });
    },
  });
};

export const usePostClaimSolAirdrop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.postClaimSolAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE],
      });
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
      });
    },
  });
};

export const useGetSummaryPoint = (enabled: boolean) => {
  return useQuery({
    queryKey: [EQueryKeys.SUMMARY_POINT],
    queryFn: () => AirdropServices.getSummaryPoint(),
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetInfiniteCreatorAirdropList = (enabled: boolean) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getClaimedCreatorAirdrop({
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
    enabled,
  });
};

export const useGetInfiniteCreatorAirdropTotalByToken = (enabled: boolean) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.CREATOR_AIRDROP_TOTAL_BY_TOKEN],
    queryFn: async ({ pageParam }) => {
      const res = await AirdropServices.getCreatorAirdropTotalByToken({
        page: pageParam,
        perPage: 10,
      });
      return res;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
    enabled,
  });
};

export const useGetTotalAirdropInfo = (enabled: boolean = true) => {
  return useQuery({
    queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
    queryFn: () => AirdropServices.getTotalAirdropInfo(),
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetAirdropRankConfig = () => {
  return useQuery({
    queryKey: [EQueryKeys.AIRDROP_RANK_CONFIG],
    queryFn: () => AirdropServices.getAirdropRankConfig(),
    refetchOnWindowFocus: false,
  });
};

export const useGetEventAirdrops = (params: IListEventAirdropParams) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.EVENT_AIRDROP_LIST],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getEventAirdrop({
        page: pageParam,
        limit: 10,
        ...params,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
    refetchOnWindowFocus: false,
  });
};
