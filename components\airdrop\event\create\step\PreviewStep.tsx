import SvgCircle from '@/components/shared/SvgCircle';
import DatabaseIcon from '@/public/icons/database.svg';
import { useFormContext } from 'react-hook-form';
import { EventAirdropSchemaFormType } from '../schema';
import QuestionMarkIcon from '@/public/icons/question-mark-icon.svg';
import { format } from 'date-fns';
import { FaAngleRight } from 'react-icons/fa';

const PreviewStep = () => {
  const { getValues } = useFormContext<EventAirdropSchemaFormType>();
  const formValues = getValues();
  return (
    <div className="w-full rounded-std bg-white py-6 px-5 flex flex-col gap-6">
      <div className="w-full flex items-center gap-[18px]">
        <div className="rounded-full bg-primary-500/10 flex items-center justify-center size-12">
          <DatabaseIcon />
        </div>
        <span className="text-gray-800 font-medium text-xl">AirDrop</span>
      </div>
      <div className="flex gap-[18px]">
        <div className="relative">
          <SvgCircle progress={0} />
          <div
            className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center cursor-pointer"
            //   onClick={onRedirect}
          >
            {/* {icon ? (
            <Image
              src={icon}
              alt={'Airdrop Logo'}
              width={48}
              height={48}
              className="rounded-full size-12 aspect-square object-cover"
            />
          ) : ( */}
            <div className="rounded-full size-12 bg-gradient-to-b from-[#ffab00] to-[#ff4b00] flex justify-center items-center">
              <QuestionMarkIcon />
            </div>
            {/* )} */}
          </div>
        </div>
        <div className="grow flex items-center justify-between">
          <div className="flex flex-col gap-2.5">
            <span className="text-gray-900 font-medium cursor-pointer leading-none">
              {formValues.eventName ? formValues.eventName : 'AAAAAAAAAAA'}
            </span>
            <span className="text-xs leading-[17px] py-1 px-2.5 rounded-lg bg-primary-500/10 text-primary-500">
              {formValues.eventStartedAt ? format(formValues.eventStartedAt, 'd MMM') : '???'}
            </span>
          </div>
        </div>
        <div className="border border-gray-200 px-[18px] py-3.5">
          <div className="py-3">
            <span>AirDrop per Person</span>
            <span>
              0.002 SOL <FaAngleRight />
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewStep;
