export enum EQueryKeys {
  PROFILE = 'profile',
  PROCESS_TASK = 'processTask',
  CONTENTS = 'contents',
  CONTENT_DETAIL = 'contentDetail',
  INVITE_INFO = 'inviteInfo',
  MY_NFT = 'myNft',
  NFT_COLLECTION = 'nftCollection',
  TOTAL_HOLD = 'totalHold',
  NFT_DETAIL = 'nftDetail',
  TASK_RANKING = 'taskRanking',
  TRANSFER_HISTORY = 'transferHistory',
  NATIVE_BALANCE = 'nativeBalance',
  RECENT_TRANSFER = 'recentTransfer',
  POINT_HISTORY = 'pointHistory',
  NFT_COLLECTION_EXTRA = 'nftCollectionExtra',
  LIST_INVITE_TASKS = 'getListInviteTasks',
  PFP_IMAGES = 'pfpImages',
  PFP_CATEGORIES = 'pfpCategories',
  PFP_TASKS = 'pfpTasks',
  GATCHA_CONFIG = 'gatchaConfig',
  GATCHA_IMAGE_DETAIL = 'gatchaImageDetail',
  MY_INFINITE_ALL_COLLECTION_ITEM = 'myInfiniteAllCollectionItem',
  SOLANA_TRANSFER_HISTORY = 'solanaTransferHistory',
  USER_RANK = 'userRank',
  ROCKET_PACK_LIST = 'rocketPackList',
  ROCKET_PACK_DETAIL = 'rocketPackDetail',
  ROCKET_INVOICE = 'rocketInvoice',
  ROCKET_HISTORY = 'rocketHistory',
  TOTAL_USER = 'totalUser',
  ALL_STAKING = 'allStaking',
  STAKING_DETAIL = 'stakingDetail',
  TOTAL_NFT_AMOUNT = 'getTotalAmount',
  GET_TASK_RATIO = 'getTaskRatio',

  MEME_LIST = 'memeList',
  MEME_INFINITE_LIST = 'memeInfiniteList',
  MY_MEME_LIST = 'myMemeList',
  MEME_DETAIL = 'memeDetail',
  MEME_TRADE_HISTORY = 'memeTradeHistory',
  MEME_TRADING_VIEW = 'memeTradingView',
  MEME_CREATE = 'memeCreate',
  ALL_MY_MEME = 'allMyMeme',
  MEME_MCAP_LIST = 'memeMCapList',

  SOL_TOKEN_BALANCE = 'sol-token-balance',
  SOL_BALANCE = 'sol-balance',
  TOKEN_OWNER_BALANCE = 'token-owner-balance',
  SOL_TOKEN_PRICE = 'solTokenPrice',
  MEME_PAD_WAIT_LIST_INFO = 'memePadWaitListInfo',
  NUMBER_ACCOUNT = 'numberAccount',
  KEY_PHRASE = 'keyPhrase',

  AIRDROP_LIST = 'airdropList',
  AIRDROP_LIST_INFINITE = 'airdropListInfinite',
  AIRDROP_DETAIL = 'airdropDetail',
  SOL_AIRDROP_LIST = 'solAirdropList',
  SOL_AIRDROP_LIST_INFINITE = 'solAirdropListInfinite',
  SUMMARY_AIRDROP = 'summaryAirdrop',
  SOL_PRICE_AIRDROP = 'solPriceAirdrop',
  SUMMARY_POINT = 'summaryPoint',
  CREATOR_AIRDROP = 'creatorAirdrop',
  CREATOR_AIRDROP_LIST_INFINITE = 'creatorAirdropListInfinite',
  CREATOR_AIRDROP_TOTAL_BY_TOKEN = 'creatorAirdropTotalByToken',
  NEW_USER_POINT_INFO = 'newUserPointInfo',
  NEW_USER_RANKING_DETAIL = 'newUserRankingDetail',
  AIRDROP_RANK_CONFIG = 'airdropRankConfig',
  EVENT_AIRDROP_LIST = 'eventAirdropList',

  INFINITE_DEX_COINS = 'infiniteDexCoins',
  DEX_COINS = 'dexCoins',
  DEX_COIN_DETAIL = 'dexCoinDetail',
  TOTAL_AIRDROP_INFO = 'totalAirdropInfo',
  EST_BUY_AMOUNT = 'estBuyAmount',
  BONDING_CURVE_DATA = 'bondingCurveData',
  SMART_CONTRACT_METADATA = 'smartContractMetadata',

  AIRDROP_SP_INFO = 'airdropSpInfo',
}
