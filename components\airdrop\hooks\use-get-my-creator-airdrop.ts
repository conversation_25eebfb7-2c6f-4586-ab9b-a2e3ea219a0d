import { useBondingCurveSdkContext } from '@/contexts/sdk.context';
import { useAuth } from '@/hooks/use-auth';
import { ICoin, useGetAllMyMeme } from '@/services/meme-launchpad';
import { EQueryKeys } from '@/services/query-keys';
import { convertMistToDec } from '@/utils/helper';
import { PublicKey } from '@solana/web3.js';
import { useQuery } from '@tanstack/react-query';

export const MIN_AVAILABLE_BALANCE = 890880;

export const useGetMyCreatorAirdrop = (enabled: boolean = true) => {
  const { isAuthenticated } = useAuth();
  const { sdk, connection } = useBondingCurveSdkContext();

  const { data: myCoins, isLoading } = useGetAllMyMeme(isAuthenticated);

  const creatorAirdropQuery = useQuery({
    queryKey: [EQueryKeys.CREATOR_AIRDROP, myCoins],
    queryFn: async (): Promise<Array<ICoin & { balance: number | string; creatorPub: string }>> => {
      if (!myCoins || !myCoins.length) return [];

      if (!sdk || !connection) return [];

      //Get creator pubkey from token address
      const creators = myCoins.map((coin) => {
        const [creatorPub] = sdk.findCreatorFeeRecipientAccount(new PublicKey(coin.tokenAddress));

        return { ...coin, creatorPub: creatorPub.toBase58() };
      });

      //Filter creators to only those with a valid creatorPub
      const filteredCreators = creators.filter((coinAccount) => !!coinAccount.creatorPub);

      //Get balance for each creator's pubkey
      const account = await connection.getMultipleAccountsInfo(
        filteredCreators.map((coin) => new PublicKey(coin.creatorPub))
      );

      const coinWithBalance = filteredCreators.map((coin, index) => {
        const acc = account[index];
        return {
          ...coin,
          balance: acc?.lamports || 0,
        };
      });

      //Filter accounts to only those with a balance above the minimum available balance
      const filteredCoinWithBalance = coinWithBalance.filter(
        (data) => data.balance && data.balance > MIN_AVAILABLE_BALANCE
      );

      const parsedCoinWithBalance = filteredCoinWithBalance.map((coin) => ({
        ...coin,
        balance: convertMistToDec(coin.balance - MIN_AVAILABLE_BALANCE),
      }));

      return parsedCoinWithBalance;
    },
    enabled: myCoins && !!sdk && !!connection && enabled,
  });

  return {
    ...creatorAirdropQuery,
    isLoading: creatorAirdropQuery.isLoading || isLoading,
    allMyCoins: myCoins,
  };
};
