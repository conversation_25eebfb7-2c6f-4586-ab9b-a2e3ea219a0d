import { chainList, Token } from '@/lib/web3/constants';
import { cn } from '@/utils/common';
import Image from 'next/image';
import { ReactElement } from 'react';
// import { useBalance } from './hooks/use-balance';
// import { round } from 'lodash';
// import { useSolBalance } from '@/hooks/use-sol-balance';
// import { useAuth } from '@/hooks/use-auth';

export const TokenItem = ({
  token,
  price,
  onClick,
  tokenBalance,
  tokenUsdBalance,
}: {
  price?: number;
  token: Token;
  onClick?: () => void;
  tokenBalance?: string | ReactElement;
  tokenUsdBalance?: string | ReactElement;
}) => {
  // const { user } = useAuth();
  // const { solanaBalance } = useSolBalance();
  // const balance = useBalance(token);
  // const displayBalance = token.symbol === 'SOL' && user?.solana_address ? solanaBalance : balance;
  return (
    <div
      key={token.symbol}
      className={cn(
        'py-4 flex gap-[18px] items-center',
        'text-[#4C4C4C] font-medium',
        onClick && 'cursor-pointer'
      )}
      onClick={onClick}
    >
      <div>
        <div className={'rounded-full border border-[#E5E5E5]/50'}>
          <Image
            src={token.icon}
            alt={token.symbol}
            width={48}
            height={48}
            className="size-12 rounded-full object-cover min-w-12"
          />
        </div>
      </div>
      <div className={'flex flex-col gap-1'}>
        <div className={'flex gap-1'}>
          <span className={'text-base'}>{token.symbol}</span>
          <Image
            src={chainList[token.chainId]?.icon}
            alt={chainList[token.chainId]?.name}
            width={16}
            height={16}
          />
        </div>
        <span className={'text-[#999] text-xs font-medium'}>{token.name}</span>
      </div>
      <div className={'flex-1 flex-col flex items-end'}>
        <span className={'text-base whitespace-nowrap'}>
          {tokenBalance || 0} {token.symbol}
        </span>
        <span className={'text-xs font-medium text-[#999]'}>
          {tokenUsdBalance || !price ? tokenUsdBalance : 0} $
        </span>
      </div>
    </div>
  );
};
