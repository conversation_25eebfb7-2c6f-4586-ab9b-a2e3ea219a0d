import { EAirdropStatus } from '@/services/airdrop';

export const getBtnClass = (status: EAirdropStatus) => {
  switch (status) {
    case EAirdropStatus.SOON:
      return '!bg-gray-200 !text-gray-600';
    case EAirdropStatus.CHECK:
      return '!bg-gray-200 !text-gray-600';
    default:
      return '';
  }
};

export const getStatusText = (status: EAirdropStatus) => {
  switch (status) {
    case EAirdropStatus.CLAIM:
      return 'Claim';
    case EAirdropStatus.CHECK:
      return 'Check';
    default:
      return 'Soon';
  }
};

export enum EAirdropTab {
  EVENT = 'Event',
  RANK = 'Rank',
  CREATOR = 'Creator',
}

export const MIN_SOL_CLAIMABLE = 0.0001; // Minimum amount to be claimable in SOL
