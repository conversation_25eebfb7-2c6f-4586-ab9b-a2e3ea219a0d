import Button from '@/components/shared/Button';
import PopUpModal from '@/components/shared/PopUpModal';
import { useDisclosure } from '@/hooks/use-disclosure';
import clsx from 'clsx';
import React, { useState } from 'react';
import { IoChevronDownSharp } from 'react-icons/io5';
import { EImportAddressType } from '../../common';
import AirdropAddressTable, { IAirdropAddressTableProps } from './AirdropAddressTable';

const importTypeOptions = [
  {
    label: 'Token Holder',
    value: EImportAddressType.TOKEN_HOLDER,
    importLabel: 'Contract Address',
  },
  {
    label: 'NFT Holder',
    value: EImportAddressType.NFT_HOLDER,
    importLabel: 'Contract Address',
  },
  {
    label: 'Import files',
    value: EImportAddressType.IMPORT_FILE,
    importLabel: 'File',
  },
  {
    label: 'X Reply Import',
    value: EImportAddressType.X_REPLY_IMPORT,
    importLabel: 'X Post URL',
  },
];

const getImportTypeLabel = (type: EImportAddressType) => {
  const option = importTypeOptions.find((option) => option.value === type);
  return option ? option.label : '';
};

const getImportFieldLabel = (type: EImportAddressType) => {
  const option = importTypeOptions.find((option) => option.value === type);
  return option ? option.importLabel : '';
};

export default function AirdropAddressSectionForm() {
  const [currentImportType, setCurrentImportType] = useState<EImportAddressType>(
    EImportAddressType.TOKEN_HOLDER
  );
  const [airdropAddressParams] = useState<IAirdropAddressTableProps['params'] | undefined>();

  const importTypeModal = useDisclosure();

  const onImportAddress = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const formData = new FormData(e.currentTarget);
    console.log({ formData });
  };

  return (
    <>
      <form
        id="airdrop-address"
        className="flex flex-col p-[18px] rounded-std bg-white gap-5 border border-gray-200"
        onSubmit={onImportAddress}
      >
        <div className="w-full flex flex-col gap-2.5">
          <span className="text-gray-800">AirDrop Address</span>
          <div
            className="w-full flex items-center justify-between border border-gray-200 bg-gray-300 rounded-2xl p-4 input-text-gray-600-black cursor-pointer"
            onClick={importTypeModal.open}
          >
            {getImportTypeLabel(currentImportType)}
            <IoChevronDownSharp size={20} color="#666" />
          </div>
        </div>
        <div className="w-full flex flex-col gap-2.5">
          <span className="text-gray-800 ">{getImportFieldLabel(currentImportType)}</span>
          <input
            type="text"
            name="contractAddress"
            className="border border-gray-200 bg-gray-300 rounded-2xl p-4 input-text-gray-600-black"
            placeholder="Add Contract Address"
          />
        </div>
        <div className="w-full flex flex-col gap-2.5">
          <div className="w-full flex justify-between text-gray-800 text-sm">
            <span>Transaction cost</span>
            <span>0.0001 SOL</span>
          </div>
          {!airdropAddressParams ? (
            <Button
              form="airdrop-address"
              type="submit"
              className="!w-full"
              onClick={(e) => e.stopPropagation()}
            >
              Import Address
            </Button>
          ) : (
            <AirdropAddressTable params={airdropAddressParams} />
          )}
        </div>
      </form>
      <PopUpModal
        isOpen={importTypeModal.opened}
        onClose={importTypeModal.close}
        className="py-6 px-[18px]"
      >
        <div className="w-full flex flex-col">
          {importTypeOptions.map((type) => (
            <div
              key={type.value}
              className={clsx(
                'w-full py-[14px] px-3 text-gray-800 hover:rounded-xl hover:bg-gray-300 cursor-pointer',
                type.value === currentImportType ? 'bg-gray-300 rounded-xl' : ''
              )}
              onClick={() => {
                if (currentImportType !== type.value) setCurrentImportType(type.value);
                importTypeModal.close();
              }}
            >
              {type.label}
            </div>
          ))}
        </div>
      </PopUpModal>
    </>
  );
}
