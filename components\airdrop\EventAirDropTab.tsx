import clsx from 'clsx';
import Button, { BUTTON_SIZE } from '../shared/Button';
import FetchDataStatementWrapper from '../shared/FetchDataStatementWrapper';
import AirDropItem from './AirDropItem';
import { getBtnClass, getStatusText } from './common';
import { EAirdropStatus, EEventAirdropStatus, useGetEventAirdrops } from '@/services/airdrop';
import { range } from 'lodash';
import EventAirdropItem from './EventAirdropItem';
import { useMemo } from 'react';
import LoadMoreButton from '../shared/LoadMoreButton';

interface Props {
  currentFilter: EEventAirdropStatus;
}

export default function EventAirDropTab({ currentFilter }: Props) {
  const {
    data: eventAirdrops,
    hasNextPage,
    isLoading,
    isFetching,
    fetchNextPage,
  } = useGetEventAirdrops({ airdropStatusFilter: currentFilter });

  const flattenEventAirdrops = useMemo(() => {
    if (!eventAirdrops || !eventAirdrops.pages.length) return [];
    return eventAirdrops.pages.flatMap((page) => page.data);
  }, [eventAirdrops]);

  return (
    <FetchDataStatementWrapper
      isLoading={isLoading}
      isEmptyData={!flattenEventAirdrops.length}
      customNotDataComponent={
        <AirDropItem
          title="No items found"
          isSoon
          actionButton={
            <Button
              size={BUTTON_SIZE.SMALL}
              className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.SOON))}
              disabled
            >
              {getStatusText(EAirdropStatus.SOON)}
            </Button>
          }
        />
      }
    >
      <div className="w-full flex flex-col gap-3">
        {range(5).map((airdrop) => {
          return (
            <EventAirdropItem
              key={'event-' + airdrop}
              progress={70}
              title="AAAAAAA"
              balance="23,456"
              symbol="AAA"
              state={currentFilter}
              date={new Date()}
            />
          );
        })}

        <div className="flex justify-center w-full">
          <LoadMoreButton
            hasNextPage={!!hasNextPage}
            isFetching={isFetching}
            fetchNextPage={fetchNextPage}
          >
            View More
          </LoadMoreButton>
        </div>
      </div>
    </FetchDataStatementWrapper>
  );
}
