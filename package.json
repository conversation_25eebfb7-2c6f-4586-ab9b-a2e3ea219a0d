{"name": "izanaweb-user", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:check": "eslint . --ext ts --ext tsx --ext js", "lint:fix": "eslint . --ext .ts,.tsx,.js --fix && stylelint '**/*.css' --fix", "prettier": "prettier --check \"(*.tsx|*.ts|*.css|*.scss)\"", "prettier:fix": "prettier --write \"(*.tsx|*.ts|*.css|*.scss)\"", "commitlint": "commitlint --edit", "prepare": "husky"}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.1.1", "@rainbow-me/rainbowkit": "^2.1.4", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.98.0", "@solflare-wallet/metamask-wallet-standard": "^1.0.11", "@svgr/webpack": "^8.1.0", "@tanstack/query-core": "^5.51.21", "@tanstack/react-query": "^5.51.23", "@tonconnect/ui-react": "^2.0.11", "@types/async-retry": "^1.4.9", "@types/merge-images": "^1.2.4", "@types/validator": "^13.15.1", "@web3auth/auth-adapter": "^9.4.0", "@web3auth/base": "^9.4.0", "@web3auth/ethereum-provider": "^9.4.0", "@web3auth/no-modal": "^9.4.0", "@web3auth/openlogin-adapter": "^8.12.2", "@web3auth/solana-provider": "^9.5.0", "@web3auth/web3auth-wagmi-connector": "^6.0.0", "alchemy-sdk": "^3.4.1", "async-retry": "^1.3.3", "axios": "^1.7.4", "bignumber.js": "^9.3.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "ethers": "^6.13.2", "gapi-script": "^1.2.0", "husky": "^9.1.4", "jwt-decode": "^4.0.0", "lint-staged": "^15.2.9", "lit-html": "^3.2.0", "lodash": "^4.17.21", "merge-images": "^2.0.0", "next": "^14.2.5", "pino-pretty": "^11.2.2", "prettier": "^3.3.3", "qr-scanner": "^1.4.2", "qrcode.react": "^4.0.1", "react": "^18", "react-cropper": "^2.3.3", "react-datepicker": "^8.7.0", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-icons": "^5.3.0", "react-modal": "^3.16.1", "react-player": "^2.16.0", "react-share": "^5.2.2", "react-spring": "^9.7.4", "react-toastify": "^10.0.5", "react-use-draggable-scroll": "^0.4.7", "rxjs": "^7.8.1", "sharp": "^0.33.5", "siwe": "^2.3.2", "socket.io-client": "^4.7.5", "swiper": "^11.2.10", "tailwind-merge": "^2.5.2", "usehooks-ts": "^3.1.0", "validator": "^13.15.15", "viem": "^2.19.4", "wagmi": "^2.12.5", "zod": "^4.1.5"}, "devDependencies": {"@commitlint/config-conventional": "^19.2.2", "@types/lodash": "^4", "@types/node": "^22.3.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3", "@typescript-eslint/eslint-plugin": "^8.1.0", "commitlint": "^19.4.0", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{ts,tsx}": "eslint --cache --fix", "*.{ts,tsx,css,md,json}": "prettier --write --ignore-unknown"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}