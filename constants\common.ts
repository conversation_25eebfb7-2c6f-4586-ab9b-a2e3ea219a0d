import DefaultImage from '@/public/images/smart-pocket-default-image.png';
import DexScreenerIcon from '@/public/icons/dex-screener.svg';
import DextoolIcon from '@/public/icons/dex-tool.svg';
import DiscordIcon from '@/public/icons/discord.svg';
import FacebookIcon from '@/public/icons/facebook.svg';
import InstagramIcon from '@/public/icons/instagram.svg';
import TelegramIcon from '@/public/icons/telegram.svg';
import TiktokIcon from '@/public/icons/tiktok.svg';
import TwitterIcon from '@/public/icons/twitter.svg';
import YoutubeIcon from '@/public/icons/youtube.svg';
import WebsiteIcon from '@/public/icons/website.svg';
import LinkIcon from '@/public/icons/link.svg';

export const ApiBaseUrl = process.env.NEXT_PUBLIC_API_URL;
export const SocketDomain =
  process.env.NEXT_PUBLIC_SERVER_DOMAIN || 'https://api.dev-smartpocke.sotatek.works';

export const DISCOVERY_DOCS = ['https://www.googleapis.com/discovery/v1/apis/youtube/v3/rest'];
export const SCOPES = 'https://www.googleapis.com/auth/youtube.readonly';

export const DefaultImageSrc = DefaultImage.src;

export enum RESPONSE_CODE {
  SUCCESS = 200,
  UNAUTHORIZED = 401,
  BAD_REQUEST = 400,
  NOT_FOUND = 404,
  PERMISSION = 403,
  SERVER_ERROR = 500,
  VALIDATION_ERROR = 422,
}

export const routePaths = {
  login: '/login',
  home: '/home',
  profile: '/profile',
  error: '/error',
  notFound: '/not-found',
  list: (type?: string | number) => (type ? '/list/' + type : '/list'),
  wallet: '/wallet',
  token: '/wallet/token',
  send: '/wallet/token/send',
  deposit: '/wallet/token/deposit',
  content: (id: string) => `/content/${id}`,
  incompleteTask: '/incomplete-task',
  use: (id?: string | number | null): string => (id ? `/use/${id}` : '/use'),
  useShow: '/use/show',
  logout: '/logout',
  googleSeedPhrase: '/google-login-user-wallet',
  nftCollection: (type: string, collectionId: string, collectionName: string) =>
    `/nft-collection/${type}/${collectionId}/${collectionName}`,
  nftDetail: (nftId: string) => `/nft-detail/${nftId}`,
  sbtDetail: (id: string) => `/sbt-detail/${id}`,
  itemDetail: (type: string, id: string) => `/item-detail/${type}/${id}`,
  editProfile: '/account/edit',
  invite: '/invite',
  notice: '/notice',
  account: '/account',
  airdrop: '/airdrop',
  airdropConfirm: '/airdrop-sp',
  airdropInvestor: '/airdrop-investor',
  pfp: '/pfp',
  exchange: '/exchange',
  exchangeTask: '/exchange-task',
  authCallback: '/auth/callback',
  shop: '/shop',
  verifySolanaAddress: '/verify-solana-address',
  collab: '/collab',
  staking: '/wallet/token/staking',
  newStaking: '/wallet/token/staking/new',
  editStaking: (id: number) => `/wallet/token/staking/${id}`,
  memePad: '/meme-pad',
  takingOver: '/taking-over',
  transferAccount: '/transfer-account',
  memePadDetail: (coinId: string) => `/meme-pad/${coinId}`,
  memePadTrade: (coinId: string) => `/meme-pad/${coinId}/trade`,
  swap: '/wallet/token/swap',
  walletMemeDetail: (address: string) => `/wallet/meme/${address}`,
  dexMemeDetail: (coinId: string) => `/meme-pad/dex/${coinId}`,
  //Airdrop event
  airdropEventCreate: '/airdrop/event/create',
  airdropEventDetail: (id: string) => `/airdrop/event/${id}`,
};

export const OUTER_LINKS = {
  DISCORD: process.env.NEXT_PUBLIC_DISCORD_URL,
  X: process.env.NEXT_PUBLIC_X_URL,
  POKEMY_X: process.env.NEXT_PUBLIC_POKEMY_X_URL,
  TERMS: process.env.NEXT_PUBLIC_TERM_URL,
  PRIVACY: process.env.NEXT_PUBLIC_PRIVACY_URL,
  TELEGRAM: process.env.NEXT_PUBLIC_TELEGRAM_URL,
  TELEGRAM_COMMUNITY: process.env.NEXT_PUBLIC_TELEGRAM_COMMUNITY_URL,
  MARKETPLACE: {
    HOME: process.env.NEXT_PUBLIC_MARKETPLACE_URL,
    EXPLORE: process.env.NEXT_PUBLIC_MARKETPLACE_EXPLORE_URL,
    COLLECTIONS: process.env.NEXT_PUBLIC_MARKETPLACE_COLLECTIONS_URL,
    CREATE_NFT: process.env.NEXT_PUBLIC_MARKETPLACE_CREATE_NFT_URL,
    SEARCH_LINK: process.env.NEXT_PUBLIC_MARKETPLACE_SEARCH_LINK,
  },
  SHARE_X: process.env.NEXT_PUBLIC_X_SHARE_URL,
  SHARE_TELEGRAM: process.env.NEXT_PUBLIC_TELEGRAM_SHARE_URL,
  LOGIN_WITH_TELEGRAM: process.env.NEXT_PUBLIC_TELEGRAM_LOGIN,
  FIRST_TASK_LINK: process.env.NEXT_PUBLIC_FIRST_TASK_LINK,
  FIRST_TASK_SBT_ID: process.env.NEXT_PUBLIC_FIRST_TASK_SBT_ID,
  TELEGRAM_INVITE_LINK: process.env.NEXT_PUBLIC_TELEGRAM_INVITE,
  SP_OFFICIAL_URL: process.env.NEXT_PUBLIC_SP_OFFICIAL_SITE_URL,
};

export const TASK_TYPE = {
  X_LIKE: 'x_like',
  X_REPOST: 'x_repost',
  YOUTUBE_WATCH: 'yt_view',
  YOUTUBE_COMMENT: 'yt_comment',
  YOUTUBE_SUBSCRIBE: 'yt_subscribe',
};

export const WEB_SOCKET_EVENT = {
  MINT_TO_USER: {
    IN_PROGRESS: 'IN_PROGRESS',
    SUCCESS: 'MINT_NFT_TO_USER_SUCCESS',
    FAIL: 'MINT_NFT_TO_USER_FAIL',
  },
  AUTH: {
    LOGIN_TELEGRAM_SUCCESS: 'LOGIN_TELEGRAM_SUCCESS',
  },
  MINT_PFP_USER: {
    IN_PROGRESS: 'MINT_PFP_IN_PROGRESS',
    SUCCESS: 'MINT_PFP_TO_USER_SUCCESS',
    FAIL: 'MINT_PFP_TO_USER_FAIL',
  },
  USER_ROCKET_INCREMENTED: 'user_rocket_incremented',
  TELEGRAM_PAYMENT: {
    SUCCESS: 'TELEGRAM_PAYMENT_SUCCESS',
    FAIL: 'TELEGRAM_PAYMENT_FAIL',
  },
};

export enum STATUS_ACTION_GET_POINT {
  CHECK = 0,
  GET = 1,
  RECEIVED = 2,
}

export const SESSION_STORAGE_KEY = {
  HEADER: 'header',
  BACKPAGE: 'backpage',
};

export const CHAIN_ID = {
  POLYGON: process.env.NEXT_PUBLIC_APP_NETWORK === 'mainnet' ? '137' : '80002',
};

export const YOUTUBE_VIDEO_PREFIX = 'https://www.youtube.com/watch?v=';
export const YOUTUBE_SHORT_PREFIX = 'https://www.youtube.com/shorts/';
export const YOUTUBE_CHANNEL_PREFIX = 'https://www.youtube.com/channel/';
export const IMAGE_REGEX = /^.*\.(jpg|jpeg|png|gif|bmp|tif|tiff|svg)$/i;
export const SPECIAL_CHAR_REGEX = /[!@~#$%^&*()_+\-=]/;

export const LOGIN_METHODS = {
  OPEN_LOGIN: 'auth',
  METAMASK: 'metamask',
} as const;

export const POST_MESSAGE_TYPE = {
  WALLET_CONNECT: 'WALLET_CONNECT',
  WALLET_LOGIN: 'WALLET_LOGIN',
  TWITTER_LOGIN: 'TWITTER_LOGIN',
  GOOGLE_LOGIN: 'GOOGLE_LOGIN',
  SEND_TOKEN: 'SEND_TOKEN',
  SCAN_QR: 'SCAN_QR',
};

export type IObjectSmaPocke = {
  osType: 'pc_os';
  sendRequestConnectWallet: () => void;
  sendRequestSignMessage: (message: string) => void;
  sendRequestMessage: (message: string) => void;
  handleFlutterMessage?: (message: any) => void;
};

export const DEFAULT_SMA_POKE: IObjectSmaPocke = {
  osType: 'pc_os',
  sendRequestConnectWallet: () => {},
  sendRequestSignMessage: (message: string) => {
    if (window.bridgeMessage && window.bridgeMessage.postMessage) {
      window.bridgeMessage.postMessage(message);
    }
  },
  sendRequestMessage: (message: string) => {
    if (window.bridgeMessage && window.bridgeMessage.postMessage) {
      window.bridgeMessage.postMessage(message);
    }
  },
  handleFlutterMessage: (message: string) => {
    console.log('message', message);
  },
};
export const CHAIN_NAME: Record<string, string> = {
  '137': 'matic',
  '11155111': 'sepolia',
  '1': 'ethereum',
  '80002': 'amoy',
};

export const EXPIRED_TOAST_ID = 'expired-toast';
export const LOG_OUT_MESSAGE = 'logout';
export const TELEGRAM_LOGIN_REFERRER_URL_LIST =
  process.env.NEXT_PUBLIC_TELEGRAM_LOGIN_REFERRER_URLS?.split(',') || [];

export const LAYOUT_BREAKPOINT = 920;

export const SOCIAL_LINKS = [
  {
    icon: WebsiteIcon,
    name: 'Website',
    key: 'websiteLink',
  },
  {
    icon: DexScreenerIcon,
    name: 'DEX Screener',
    key: 'dexScreenerLink',
  },
  {
    icon: DextoolIcon,
    name: 'DEXTools',
    key: 'dexToolsLink',
  },
  {
    icon: TwitterIcon,
    name: 'Twitter',
    key: 'twitterLink',
  },
  {
    icon: TelegramIcon,
    name: 'Telegram',
    key: 'telegramLink',
  },
  {
    icon: DiscordIcon,
    name: 'Discord',
    key: 'discordLink',
  },
  {
    icon: YoutubeIcon,
    name: 'Youtube',
    key: 'youtubeLink',
  },
  {
    icon: InstagramIcon,
    name: 'Instagram',
    key: 'instagramLink',
  },
  {
    icon: TiktokIcon,
    name: 'Tiktok',
    key: 'tiktokLink',
  },
  {
    icon: FacebookIcon,
    name: 'Facebook',
    key: 'facebookLink',
  },
  {
    icon: LinkIcon,
    name: 'Extra Link',
    key: 'extraLink',
  },
];
