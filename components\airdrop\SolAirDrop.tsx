'use client';
import Button from '../shared/Button';
import Image from 'next/image';
import { formatAddress } from '@/utils/common';

import AirDropBg from '@/public/images/airdrop-background.png';
import RefreshIcon from '@/public/icons/refresh-icon.svg';
import { useAuth } from '@/hooks/use-auth';
import {
  EAirdropStatus,
  EEventAirdropStatus,
  useGetSolAirdropList,
  useGetTotalAirdropInfo,
} from '@/services/airdrop';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useCountdown } from 'usehooks-ts';
import { useRouter, useSearchParams } from 'next/navigation';
import { Tabs } from '../shared';
import { useGetMyCreatorAirdrop } from './hooks/use-get-my-creator-airdrop';
import SideMenu from '@/features/layout/SideMenu';
import HeaderTitle from '@/features/layout/HeaderTitle';
import PopUpModal from '../shared/PopUpModal';
import { useDisclosure } from '@/hooks/use-disclosure';
import NumberDisplay from '../shared/NumberDisplay';
import { EAirdropTab } from './common';
import RankAirDropTab from './RankAirDropTab';
import EventAirDropTab from './EventAirDropTab';
import { useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '@/services/query-keys';
import CreatorAirdropTab from './CreatorAirdropTab';
import BigNumber from 'bignumber.js';
import { useIsFetching } from '@tanstack/react-query';
import Link from 'next/link';
import { routePaths } from '@/constants/common';

const getFilterTabs = (tab: EAirdropTab) => {
  switch (tab) {
    case EAirdropTab.EVENT:
      return [
        { label: 'Claim', value: EEventAirdropStatus.CLAIM, content: <></> },
        { label: 'Soon', value: EEventAirdropStatus.SOON, content: <></> },
        { label: 'Done', value: EEventAirdropStatus.DONE, content: <></> },
      ];
    case EAirdropTab.CREATOR:
      return [
        { label: 'Airdrop', value: EAirdropStatus.CLAIM, content: <></> },
        { label: 'History', value: EAirdropStatus.CHECK, content: <></> },
        { label: 'Total', value: EAirdropStatus.TOTAL, content: <></> },
      ];
    case EAirdropTab.RANK:
      return [
        { label: 'Airdrop', value: EAirdropStatus.CLAIM, content: <></> },
        { label: 'History', value: EAirdropStatus.CHECK, content: <></> },
      ];
  }
};

export default function SolAirDrop() {
  const router = useRouter();
  const queryClient = useQueryClient();

  const searchParams = useSearchParams();
  const initTab = (searchParams.get('tab') as EAirdropTab) || EAirdropTab.EVENT;

  const { user } = useAuth();

  const [isOpenSideMenu, setIsOpenSideMenu] = useState(false);
  const [claimedCreatorAirdrop, setClaimedCreatorAirdrop] = useState<string[]>([]);
  const unClaimablePopup = useDisclosure();
  const [count, { startCountdown, stopCountdown, resetCountdown }] = useCountdown({
    countStart: 60, // 1 minutes in seconds
    intervalMs: 1000,
  });

  const [eventFilterSelected, setEventFilterSelected] = useState<EEventAirdropStatus>(
    EEventAirdropStatus.CLAIM
  );
  const [solAirdropFilterSelected, setSolAirdropFilterSelected] = useState<EAirdropStatus>(
    EAirdropStatus.CLAIM
  );
  const [creatorAirdropFilterSelected, setCreatorAirdropFilterSelected] = useState<EAirdropStatus>(
    EAirdropStatus.CLAIM
  );

  //Fetch data
  const { data: canClaimTransferAirdrop } = useGetSolAirdropList({
    page: 1,
    limit: 1,
    airdropStatusFilter: EAirdropStatus.CLAIM,
  });

  const { data: myCreatorAirdrops } = useGetMyCreatorAirdrop();

  const { data: airdropInfo, isLoading: isLoadingAirdropInfo } = useGetTotalAirdropInfo();

  //Fetch data state
  const isFetchingSolAirdrops = useIsFetching({ queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE] });
  const isFetchingCreatorAirdrops = useIsFetching({ queryKey: [EQueryKeys.CREATOR_AIRDROP] });
  const isFetchingInfiniteCreatorAirdrops = useIsFetching({
    queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
  });
  const isFetchingInfiniteCreatorAirdropTotalByToken = useIsFetching({
    queryKey: [EQueryKeys.CREATOR_AIRDROP_TOTAL_BY_TOKEN],
  });

  const currentFetchingStatus = useMemo(() => {
    switch (initTab) {
      case EAirdropTab.EVENT:
        return;
      case EAirdropTab.RANK:
        return isFetchingSolAirdrops;
      case EAirdropTab.CREATOR:
        return (
          isFetchingCreatorAirdrops ||
          isFetchingInfiniteCreatorAirdrops ||
          isFetchingInfiniteCreatorAirdropTotalByToken
        );
    }
  }, [
    initTab,
    isFetchingSolAirdrops,
    isFetchingCreatorAirdrops,
    isFetchingInfiniteCreatorAirdrops,
    isFetchingInfiniteCreatorAirdropTotalByToken,
  ]);

  const refetchData = () => {
    if (initTab === EAirdropTab.RANK) {
      queryClient.invalidateQueries({ queryKey: [EQueryKeys.SOL_AIRDROP_LIST] });
      queryClient.invalidateQueries({ queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE] });
    }
    if (initTab === EAirdropTab.CREATOR) {
      switch (creatorAirdropFilterSelected) {
        case EAirdropStatus.CLAIM:
          return queryClient.invalidateQueries({ queryKey: [EQueryKeys.CREATOR_AIRDROP] });
        case EAirdropStatus.CHECK:
          return queryClient.invalidateQueries({
            queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
          });
        case EAirdropStatus.TOTAL:
          return queryClient.invalidateQueries({
            queryKey: [EQueryKeys.CREATOR_AIRDROP_TOTAL_BY_TOKEN],
          });
      }
    }
  };

  useEffect(() => {
    if (count <= 0) {
      stopCountdown();
      refetchData();
    }
  }, [count, stopCountdown]);

  const onClickRefetch = () => {
    resetCountdown();
    startCountdown();
    refetchData();
  };

  useEffect(() => {
    if (!currentFetchingStatus) {
      resetCountdown();
      startCountdown();
    }
  }, [resetCountdown, startCountdown, currentFetchingStatus]);

  const params = new URLSearchParams(searchParams.toString());

  const updateSearchParams = (key: string, value: string) => {
    params.set(key, value);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const filteredCreatorAirdrops = useMemo(() => {
    if (!myCreatorAirdrops) return [];
    return myCreatorAirdrops.filter(
      (airdrop) => !claimedCreatorAirdrop.some((address) => airdrop.tokenAddress === address)
    );
  }, [myCreatorAirdrops, claimedCreatorAirdrop]);

  const totalRankAirdrops = useMemo(() => {
    const solTransferAirdropCount = canClaimTransferAirdrop
      ? canClaimTransferAirdrop.pagination.total
      : 0;
    return solTransferAirdropCount;
  }, [canClaimTransferAirdrop]);

  const totalCreatorAirdrops = useMemo(() => {
    const myCreatorAirdropsCount = filteredCreatorAirdrops ? filteredCreatorAirdrops.length : 0;
    return myCreatorAirdropsCount;
  }, [filteredCreatorAirdrops]);

  const tabs = useMemo(
    () => [
      { label: 'Event', value: EAirdropTab.EVENT, unRead: false },
      { label: 'Rank', value: EAirdropTab.RANK, unRead: Boolean(totalRankAirdrops) },
      { label: 'Creator', value: EAirdropTab.CREATOR, unRead: Boolean(totalCreatorAirdrops) },
    ],
    [totalRankAirdrops, totalCreatorAirdrops]
  );

  const onUnClaimableClick = () => {
    unClaimablePopup.open();
  };

  const AirdropFilterTab = useMemo(
    () =>
      function FilterTab() {
        const onChangeTab = (tab: string) => {
          switch (initTab) {
            case EAirdropTab.EVENT:
              setEventFilterSelected(tab as EEventAirdropStatus);
              return;
            case EAirdropTab.RANK:
              setSolAirdropFilterSelected(tab as EAirdropStatus);
              return;
            case EAirdropTab.CREATOR:
              setCreatorAirdropFilterSelected(tab as EAirdropStatus);
              return;
          }
        };

        const getCurrentActiveTab = () => {
          switch (initTab) {
            case EAirdropTab.EVENT:
              return eventFilterSelected;
            case EAirdropTab.RANK:
              return solAirdropFilterSelected;
            case EAirdropTab.CREATOR:
              return creatorAirdropFilterSelected;
          }
        };

        return (
          <Tabs
            activeTab={getCurrentActiveTab()}
            classNameContainer="overflow-x-auto hide-scrollbar w-fit"
            classNameContent="!mt-0"
            labelClassName="flex-shrink-0 border border-gray-200 rounded-full"
            textClassName="px-5"
            onChange={onChangeTab}
            tabs={getFilterTabs(initTab)}
          />
        );
      },
    [initTab, eventFilterSelected, solAirdropFilterSelected, creatorAirdropFilterSelected]
  );

  const AirdropContent = useMemo(
    () =>
      function Content() {
        return (
          <div className="flex flex-col w-full">
            {initTab === EAirdropTab.EVENT && (
              <EventAirDropTab currentFilter={eventFilterSelected} />
            )}
            {initTab === EAirdropTab.RANK && (
              <RankAirDropTab
                currentFilter={solAirdropFilterSelected}
                onUnClaimableClick={onUnClaimableClick}
              />
            )}
            {initTab === EAirdropTab.CREATOR && (
              <CreatorAirdropTab
                claimedCreatorAirdrop={claimedCreatorAirdrop}
                setClaimedCreatorAirdrop={setClaimedCreatorAirdrop}
                currentFilter={creatorAirdropFilterSelected}
                onUnClaimableClick={onUnClaimableClick}
              />
            )}
          </div>
        );
      },
    [
      initTab,
      solAirdropFilterSelected,
      creatorAirdropFilterSelected,
      eventFilterSelected,
      claimedCreatorAirdrop,
    ]
  );

  return (
    <>
      <HeaderTitle title="Airdrop" hiddenBack />
      {/* Background */}
      <div
        className="z-0 w-full h-[392px] absolute top-0 std:rounded-t-std overflow-hidden"
        style={{
          backgroundImage: `url(${AirDropBg.src})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          zIndex: '1',
        }}
      >
        <div
          className="absolute w-full h-[148px] bottom-0"
          style={{
            background:
              'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.6) 48.76%, #F5F5F5 96.56%)',
          }}
        ></div>
      </div>
      {/* Content */}
      <div className="relative z-10 py-5 px-[18px] std:pb-0 token-slide overflow-hidden flex flex-col h-full gap-[18px] items-center">
        <div className="w-full flex flex-col gap-3 py-4 px-[18px] rounded-std items-center bg-black/30">
          <div className="w-full flex flex-col gap-1.5 px-5 py-4 rounded-xl bg-white/20">
            <span className="text-gray-300 text-sm font-medium">Total AirDrop</span>
            <div className="w-full flex justify-between items-center gap-[18px]">
              <span
                className={clsx(
                  'text-white font-medium text-xl',
                  isLoadingAirdropInfo && 'animate-pulse'
                )}
              >
                <NumberDisplay
                  number={
                    airdropInfo && airdropInfo?.total ? airdropInfo.total.toString() : '???,???'
                  }
                />
                &nbsp;<span className="text-lg">SOL</span>
              </span>
              <span className="bg-black/30 rounded-full py-1.5 px-4 text-white text-xs">
                Today :&nbsp;
                <span className="text-green-600">
                  +
                  <NumberDisplay
                    number={
                      airdropInfo
                        ? new BigNumber(airdropInfo.totalCreatorFeeToday)
                            .plus(new BigNumber(airdropInfo.totalRankFeeToday))
                            .toString()
                        : '???,???'
                    }
                  />
                </span>
              </span>
            </div>
          </div>
          <Link
            href={routePaths.airdropEventCreate}
            className="flex items-center gap-2.5 text-white font-medium"
          >
            <Image
              src="/icons/plus.svg"
              width={20}
              height={20}
              alt="plus icon"
              className="size-5 min-w-5"
            />
            Make AirDrop Event
          </Link>
        </div>

        <div className="w-full flex flex-col gap-5 px-[18px] py-5 bg-white rounded-std">
          <div className={'bg-gray-300 rounded-2xl gap-2 flex px-2 py-1.5'}>
            {tabs.map((tab) => (
              <button
                key={tab.value}
                className={clsx(
                  'flex-1 flex justify-center text-gray-800 font-medium py-2 gap-0.5',
                  initTab === tab.value && 'bg-white rounded-xl'
                )}
                onClick={() => {
                  updateSearchParams('tab', tab.value);
                }}
              >
                {tab.label}
                {tab.unRead && <span className="size-1 rounded-full bg-primary-500 mt-1"></span>}
              </button>
            ))}
          </div>
          <div className="w-full flex justify-between">
            <div
              className="flex items-center gap-2 rounded-full border pl-2.5 pr-4 py-2 cursor-pointer w-fit border-gray-200"
              onClick={() => {
                setIsOpenSideMenu(true);
              }}
            >
              <Image
                src="/icons/chain-sol.svg"
                alt="chain icon"
                width={24}
                height={24}
                className="size-6 aspect-square"
              />
              <span className="text-sm text-gray-800">
                {user?.solana_address ? formatAddress(user?.solana_address, 6) : '???'}
              </span>
            </div>
            <div
              className="ml-auto flex items-center gap-1 input-text-gray-600-black text-xs cursor-pointer"
              onClick={onClickRefetch}
            >
              <RefreshIcon /> {count} s
            </div>
          </div>
          <AirdropFilterTab />
        </div>
        <AirdropContent />
        {initTab === EAirdropTab.CREATOR && (
          <p className="input-text-gray-600-black text-xs">
            This reward is for custom artwork or creative services.
          </p>
        )}
      </div>
      <SideMenu isOpen={isOpenSideMenu} setIsOpen={setIsOpenSideMenu} isNativeApp />
      <PopUpModal
        isOpen={unClaimablePopup.opened}
        onClose={unClaimablePopup.close}
        className="!pb-5"
      >
        <div className="flex flex-col gap-6">
          <p className="text-gray-800 text-center">
            You can’t claim right now. Please wait until the token price is high enough to cover the
            transaction fee.
          </p>
          <Button className="!w-full" onClick={unClaimablePopup.close}>
            Close
          </Button>
        </div>
      </PopUpModal>
    </>
  );
}
