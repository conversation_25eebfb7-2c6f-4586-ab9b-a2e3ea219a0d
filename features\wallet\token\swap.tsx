import Button from '@/components/shared/Button';
import { formatNumberWithCommas } from '@/utils/common';
import { useEffect, useMemo, useState } from 'react';
import SwapTokenSelector, { ISwapToken, tokenOptions } from './swap-token-selector';

import { useAuth } from '@/hooks/use-auth';
import { useSwapQuote } from './hooks/use-swap-quote';
import { formatUnits } from 'viem';
import clsx from 'clsx';
import useBuildSwapTransaction, { QUOTE_ERROR_CODE } from './hooks/use-swap-transaction';
import { Connection, VersionedTransaction } from '@solana/web3.js';
import { useWallet } from '@solana/wallet-adapter-react';
import Image from 'next/image';
import { toast } from 'react-toastify';
import useSolTokenBalance from './hooks/use-sol-token-balance';
import { Controller, useForm } from 'react-hook-form';
import { MIN_AVAILABLE_BALANCE } from '@/components/airdrop/hooks/use-get-my-creator-airdrop';
import { BigNumber } from 'bignumber.js';
import { convertMistToDec } from '@/utils/helper';
import { quickNodeRpc, SOLANA_TOKEN_ADDRESS } from '@/lib/web3/constants';
import SolanaConnectButton from '@/components/shared/SolanaConnectButton';
import SwapIcon from '@/public/icons/white-swap-size-18-icon.svg';
import useTokenList from './hooks/use-token-list';
import NumberDisplay from '@/components/shared/NumberDisplay';
import { floor, round } from 'lodash';
import { usePostSwapPoint } from '@/services/auth';
import LoginWithSolanaButton from '@/components/shared/LoginWithSolanaButton';
import { debounce } from 'lodash';

interface ISwapFormData {
  amount: string;
}

const amountOfTokens = [
  { label: '25%', value: '0.25' },
  { label: '50%', value: '0.5' },
  { label: 'Max', value: '1' },
];

const USDC_TOKEN_ADDRESS = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

interface Props {
  initOutputMintAddress?: string;
  fixedSubmitButton?: boolean;
  noUnAuthText?: boolean;
}

export default function Swap({
  initOutputMintAddress,
  fixedSubmitButton = false,
  noUnAuthText = false,
}: Props) {
  const { user, isAuthenticated } = useAuth();
  const { signTransaction, connected, publicKey } = useWallet();

  const { data: initOutputMintToken } = useTokenList({
    query: initOutputMintAddress ? initOutputMintAddress : USDC_TOKEN_ADDRESS,
    limit: 10,
  });

  useEffect(() => {
    if (initOutputMintToken && initOutputMintToken.length > 0) {
      if (initOutputMintToken[0].id === tokenOptions[0].id) {
        return;
      }
      setOutputMintToken(initOutputMintToken[0]);
    }
  }, [initOutputMintToken, initOutputMintAddress]);

  const [inputMintToken, setInputMintToken] = useState<ISwapToken>(tokenOptions[0]);
  const [outputMintToken, setOutputMintToken] = useState<ISwapToken>();

  const {
    data: balances,
    refetch: refetchBalance,
    isFetching: isFetchingBalance,
  } = useSolTokenBalance(publicKey!, 'mainnet');

  const {
    control,
    handleSubmit,
    watch,
    formState: { isSubmitting, isValid },
    reset,
    setValue,
  } = useForm<ISwapFormData>({
    defaultValues: {
      amount: '',
    },
    mode: 'onChange',
  });

  const amount = watch('amount');
  const [debouncedAmount, setDebouncedAmount] = useState(amount);

  useEffect(() => {
    const handler = debounce((val) => setDebouncedAmount(val), 400);
    handler(amount);
    return () => {
      handler.cancel();
    };
  }, [amount]);

  const { data: quote, isFetching: isFetchingQuote } = useSwapQuote({
    inputMint: inputMintToken?.id || '',
    outputMint: outputMintToken?.id || '',
    amount:
      debouncedAmount && inputMintToken
        ? round(Number(debouncedAmount) * Math.pow(10, inputMintToken.decimals))
        : 0,
    enabled: !isSubmitting && isValid,
  });

  const { data: transactionInfo, isFetching: isBuildingTransaction } = useBuildSwapTransaction({
    quoteResponse: quote,
    userPublicKey: user?.solana_address || '',
    enabled: !isFetchingQuote && !!quote && !('errorCode' in quote),
  });

  const { mutateAsync: postIncreaseSwapPoint } = usePostSwapPoint();

  useEffect(() => {
    if (quote && (quote as any).errorCode) {
      const errorCode = (quote as any).errorCode;
      switch (errorCode) {
        case QUOTE_ERROR_CODE.COULD_NOT_FIND_ANY_ROUTE:
          toast.error('No route found for this swap. Please try again after a few hours.');
          break;
        default:
          toast.error('Get swap quote failed. Please try again later.');
          break;
      }
    }
  }, [quote]);

  const balance = useMemo(() => {
    if (!balances || !inputMintToken) return 0;

    const tokenBalance = balances.find((item) => item.mint === inputMintToken.id);

    return tokenBalance ? Number(tokenBalance.tokenAmount.uiAmountString) : 0;
  }, [inputMintToken, balances]);

  const outputBalance = useMemo(() => {
    if (!balances || !outputMintToken) return 0;
    const tokenBalance = balances.find((item) => item.mint === outputMintToken.id);
    return tokenBalance ? Number(tokenBalance.tokenAmount.uiAmountString) : 0;
  }, [outputMintToken, balances]);

  const maxAmount = useMemo(() => {
    if (!balance) return 0;

    if (inputMintToken?.id !== SOLANA_TOKEN_ADDRESS) return balance.toString();

    const maxBalanceCanSwap = new BigNumber(balance).minus(
      convertMistToDec(MIN_AVAILABLE_BALANCE + 1000000)
    );

    if (maxBalanceCanSwap.isLessThan(0)) {
      return 0;
    }

    return maxBalanceCanSwap.toString();
  }, [balance]);

  // const swapInfo = quote?.routePlan[0]?.swapInfo;

  // const feeMintSymbol = useMemo(() => {
  //   if (!swapInfo || !inputMintToken || !outputMintToken) return '';

  //   if (swapInfo.feeMint === inputMintToken.id) {
  //     return inputMintToken?.symbol;
  //   }
  //   if (swapInfo.feeMint === outputMintToken.id) {
  //     return outputMintToken?.symbol;
  //   }

  //   return '';
  // }, [swapInfo, inputMintToken, outputMintToken]);

  const onSubmit = async () => {
    const transactionBase64 = transactionInfo.swapTransaction;
    const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));

    if (connected && signTransaction) {
      try {
        // const { signature } = await window.solana.signAndSendTransaction(transaction as any);
        const connection = new Connection(
          quickNodeRpc.mainnet
            ? quickNodeRpc.mainnet
            : 'https://solana-mainnet.g.alchemy.com/v2/' + process.env.NEXT_PUBLIC_ALCHEMY_KEY
        );
        // const hash = await connection.getSignatureStatus(signature);
        // console.log('Transaction status:', hash, signature);
        const signedTransaction = await signTransaction(transaction);

        const transactionBinary = signedTransaction.serialize();

        const txid = await connection.sendRawTransaction(transactionBinary, {
          skipPreflight: false,
          preflightCommitment: 'confirmed',
        });

        postIncreaseSwapPoint({
          hash: txid,
          token_in: inputMintToken.id,
          token_amount: Number(amount),
          token_out: outputMintToken?.id!,
          usd_amount: quote?.swapUsdValue ? Number(quote.swapUsdValue) : 0,
        }).catch((error) =>
          toast.error('Error increasing swap points. Please try again later.' + error.message)
        );

        toast.success('Swap transaction sent successfully!');
        setTimeout(() => refetchBalance(), 2000);
        reset();
      } catch (error: any) {
        if (error.message.includes('insufficient')) {
          toast.error('Insufficient balance for this transaction. Please try again later.');
          return;
        }
        toast.error('Error signing transaction. Please try again.', error.message);
      }
    }
  };

  const inputMintUsdPrice = useMemo(() => {
    if (quote) return quote?.swapUsdValue || 0;
    return 0;
  }, [quote]);

  const swapIoToken = () => {
    if (!inputMintToken || !outputMintToken) return;
    const tmpInputMintToken = inputMintToken;
    setInputMintToken(outputMintToken);
    setOutputMintToken(tmpInputMintToken);
    reset();
  };

  const formattedOutAmount = useMemo(() => {
    if (!quote || !outputMintToken || !quote.outAmount) return '';
    const originalAmount = formatUnits(BigInt(Number(quote.outAmount)), outputMintToken.decimals);
    if (new BigNumber(originalAmount).lt(new BigNumber(1)))
      return floor(Number(originalAmount), outputMintToken.decimals);
    return formatNumberWithCommas(
      floor(Number(originalAmount), outputMintToken.decimals),
      outputMintToken.decimals
    );
  }, [quote, outputMintToken]);

  if ((!connected && isAuthenticated) || !isAuthenticated)
    return (
      <div className="flex flex-col gap-6 py-6 px-[18px] bg-white rounded-std">
        <div className="flex flex-col items-center">
          <Image
            src="/images/graduate-token.png"
            width={264}
            height={189}
            className="w-[264px]"
            alt="graduate"
          />
          {!noUnAuthText && <span className="font-medium text-gray-800">Graduation!!</span>}
        </div>
        {!connected && isAuthenticated && <SolanaConnectButton noUpdateAddress autoConnect />}
        {!isAuthenticated && <LoginWithSolanaButton />}
      </div>
    );

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={clsx('flex flex-col space-y-6', fixedSubmitButton && 'max-sm:pb-[59px]')}
    >
      <div className="flex flex-col gap-[18px]">
        <div className="relative flex flex-col gap-5">
          <Controller
            name="amount"
            control={control}
            rules={{
              required: 'Amount is required',
              validate: (value) =>
                (!isNaN(parseFloat(value)) && parseFloat(value) > 0) ||
                'Please enter a valid amount',
              max: {
                value: maxAmount,
                message: maxAmount ? `Maximum amount is ${maxAmount} SOL` : 'Insufficient balance',
              },
            }}
            render={({ field, fieldState: { error } }) => (
              <div className="relative flex justify-between items-center bg-white rounded-std p-6">
                <div className="flex flex-col gap-2.5 max-w-[60%]">
                  <input
                    type="number"
                    inputMode="decimal"
                    className="w-full max-w-full text-[28px] leading-7 text-gray-800 font-medium outline-none"
                    {...field}
                    placeholder="0"
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^\d.]/g, '');
                      const parts = value.split('.');

                      if (
                        parts.length > 2 ||
                        (parts.length === 2 &&
                          parts[1] &&
                          parts[1].length > inputMintToken.decimals)
                      ) {
                        return;
                      }
                      field.onChange(value);
                    }}
                    disabled={isFetchingBalance}
                  />
                  <span
                    className={clsx('text-gray-600 text-sm', isFetchingQuote && 'animate-pulse')}
                  >
                    ≈ $<NumberDisplay number={inputMintUsdPrice || 0} />
                  </span>
                </div>
                <div className="flex flex-col items-end gap-4">
                  <SwapTokenSelector
                    value={inputMintToken}
                    onChange={(val) => {
                      if (val.id === outputMintToken?.id) {
                        setOutputMintToken(inputMintToken);
                      }
                      setInputMintToken(val);
                    }}
                  />
                  {inputMintToken && (
                    <div className="flex flex-col items-end gap-2">
                      <div className="flex gap-1.5">
                        {amountOfTokens.map((option) => (
                          <button
                            type="button"
                            key={option.value}
                            onClick={() => {
                              if (!inputMintToken || isFetchingBalance) return;
                              setValue(
                                'amount',
                                floor(
                                  Number(new BigNumber(maxAmount).multipliedBy(option.value)),
                                  6
                                ).toString(),
                                {
                                  shouldValidate: true,
                                }
                              );
                            }}
                            className="w-fit py-1.5 px-3 text-center rounded-full text-xs transition-all border border-gray-200 bg-white text-gray-800 hover:bg-primary-500/10 hover:text-primary-500 hover:border-primary-500/30"
                          >
                            {option.label}
                          </button>
                        ))}
                      </div>
                      <span
                        className={clsx(
                          'text-[#999] text-sm font-medium whitespace-nowrap',
                          isFetchingBalance && 'animate-pulse'
                        )}
                      >
                        Max: {balance} {inputMintToken.symbol}
                      </span>
                    </div>
                  )}
                </div>
                {error && (
                  <p className="absolute bottom-3 left-6 text-red-500 text-xs">{error.message}</p>
                )}
              </div>
            )}
          />
          <div className="relative flex justify-between bg-white rounded-std p-6">
            <div
              className="absolute flex items-center justify-center -top-7 left-1/2 -translate-x-1/2 rounded-full bg-primary-500 size-10 cursor-pointer"
              onClick={swapIoToken}
            >
              <SwapIcon />
            </div>
            <div className="flex flex-col gap-2.5 max-w-[60%]">
              <input
                className="w-full max-w-full text-[28px] leading-7 text-gray-800 font-medium outline-none"
                value={formattedOutAmount}
                placeholder="0"
                readOnly
              />
              <div className="flex items-center gap-1.5">
                <span className={'text-gray-600 text-sm'}>
                  ≈ $<NumberDisplay number={inputMintUsdPrice} isNormalize />
                </span>
                {!!inputMintUsdPrice && (
                  <span className="rounded-full bg-primary-500/10 text-primary-500 text-xs font-medium py-1.5 px-[14px]">
                    +{formatNumberWithCommas(floor(Number(inputMintUsdPrice)))}
                    &nbsp;PT
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end gap-4">
              <SwapTokenSelector
                value={outputMintToken}
                onChange={(val) => {
                  if (val.id === inputMintToken?.id && outputMintToken) {
                    setInputMintToken(outputMintToken);
                  }
                  setOutputMintToken(val);
                }}
              />
              {outputMintToken && (
                <span
                  className={clsx(
                    'text-[#999] text-sm font-medium whitespace-nowrap',
                    isFetchingBalance && 'animate-pulse'
                  )}
                >
                  {outputBalance} {outputMintToken.symbol}
                </span>
              )}
            </div>
          </div>
        </div>
        <div
          className={clsx(
            'flex flex-col gap-6 py-2.5 px-[18px] bg-white rounded-2xl',
            fixedSubmitButton && 'max-sm:hidden'
          )}
        >
          {/* <div className="flex flex-col gap-5">
            <div className="flex items-center gap-5">
              <Checkbox
                checked={feePayer === FeePayer.FROM_WALLET_BALANCE}
                onChange={() => setFeePayer(FeePayer.FROM_WALLET_BALANCE)}
              />
              <div className="flex flex-col gap-2 grow">
                <span className="text-gray-800 font-medium">From wallet balance</span>
                <div className="flex w-full justify-between items-center text-xs input-text-gray-600-black font-medium">
                  <span
                    className={clsx(
                      (isFetchingQuote || isBuildingTransaction) && 'animate-pulse w-20 h-6'
                    )}
                  >
                    Fee -
                    {transactionInfo
                      ? formatNumberWithCommas(formatUnits(BigInt(transactionInfo.chainFee!), 9))
                      : 0}
                    &nbsp;SOL
                  </span>
                  <span>{formatAddress(get(user, 'solana_address', 'XxxxxxXXXX')!, 4)}</span>
                </div>
              </div>
            </div>
          </div> */}
          {inputMintToken?.name === 'sp' && (
            <div className="flex flex-col gap-5">
              <span className="input-text-gray-600-black text-lg font-medium">Details</span>
              <div className="w-full flex justify-between items-center py-4 px-2">
                <span className="text-gray-800 font-medium">Price</span>
                <span className="text-gray-600 font-medium">Price</span>
              </div>
              <div className="w-full flex justify-between items-center py-4 px-2">
                <span className="text-gray-800 font-medium">Holding Period</span>
                <span className="text-gray-600 font-medium">Price</span>
              </div>
              <div className="w-full flex justify-between items-center py-4 px-2">
                <span className="text-gray-800 font-medium">Get On</span>
                <span className="text-gray-600 font-medium">Price</span>
              </div>
            </div>
          )}
          <div className={clsx('w-full', fixedSubmitButton ? 'max-sm:hidden' : '')}>
            <Button
              type="submit"
              className="w-full max-w-[600px]"
              isLoading={isSubmitting || isBuildingTransaction}
              disabled={!transactionInfo || !isValid}
            >
              Swap Now
            </Button>
          </div>
        </div>
        <div
          className={clsx(
            'w-full',
            fixedSubmitButton
              ? 'sm:hidden max-sm:fixed max-sm:bottom-[77px] max-sm:left-0 max-sm:bg-white max-sm:py-[14px] max-sm:px-[18px] max-sm:border-t max-sm:border-gray-200 max-sm:flex max-sm:justify-center'
              : 'hidden'
          )}
        >
          <Button
            type="submit"
            className="w-full max-w-[600px]"
            isLoading={isSubmitting || isBuildingTransaction}
            disabled={!transactionInfo || !isValid}
          >
            Swap Now
          </Button>
        </div>
      </div>
    </form>
  );
}
