'use client';
import AirdropThumb from '@/public/images/airdrop-thumb.png';
import AirdropBg from '@/public/images/airdrop-bg.png';
import Image from 'next/image';
import clsx from 'clsx';
// import NumberDisplay from '../shared/NumberDisplay';
import { formatNumberWithCommas } from '@/utils/common';
import { floor } from 'lodash';
import { useGetTotalAirdropSpInfo } from '@/services/airdropSp/airdrop.queries';
import AirdropTokens from './AirdropTokens';
import ClaimAirdropSP from './ClaimAirdropSP';

export default function AirdropSP() {
  const { data: airdropSpInfo, isLoading: loadingTotalAirdrop } = useGetTotalAirdropSpInfo();
  const isAirdropped = false;
  return (
    <>
      <div
        className="w-full h-60 flex flex-col-reverse absolute top-0 sm:rounded-std overflow-hidden"
        style={{
          backgroundImage: `url(${AirdropBg.src})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          zIndex: '1',
        }}
      >
        <div
          className="h-36"
          style={{
            background:
              'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.6) 48.76%, #F5F5F5 96.56%)',
          }}
        ></div>
      </div>
      <div className="flex flex-col relative z-10 pt-10 px-[18px] grow">
        <div className="flex flex-col gap-5 items-center grow">
          <Image
            src={AirdropThumb.src}
            alt="Airdrop Thumbnail"
            width={160}
            height={0}
            layout="intrinsic"
          />
          <div className="flex flex-col text-center">
            <span className="text-gray-800 text-xl font-medium">Be ready for $SP AirDrop!!</span>
            <span className="text-gray-600 text-base ">October 2025</span>
          </div>
          <div className="w-full flex flex-col gap-2.5 grow">
            <div className="flex flex-col bg-white rounded-std pt-5 pb-2.5 px-[18px] gap-2.5 grow">
              <div className="flex flex-col gap-2.5">
                <div className="w-full flex flex-col gap-1 bg-gray-300 py-4 px-5 rounded-2xl">
                  <span className="text-gray-600 text-sm font-medium">Total $SP Airdrop</span>
                  <div className="w-full flex justify-between">
                    <span
                      className={clsx(
                        'text-gray-800 text-xl font-medium',
                        loadingTotalAirdrop && 'animate-pulse'
                      )}
                    >
                      {formatNumberWithCommas(floor(airdropSpInfo?.totalSp || 0))}
                      <span className="text-sm"> SP</span>
                    </span>
                    <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium">
                      Today :&nbsp;
                      {/* // TODO: add loading state */}
                      <span className={clsx('text-green-600', false && 'animate-pulse')}>
                        {/* TODO: fix data display */}
                        +0SP
                        {/* <NumberDisplay number={'1234'} /> */}
                      </span>
                    </span>
                  </div>
                </div>
                {isAirdropped ? (
                  <ClaimAirdropSP />
                ) : (
                  <AirdropTokens airdropSpInfo={airdropSpInfo} />
                )}
              </div>
            </div>
            {/* {!isAirdropped && (
              <span className="text-center text-xs text-gray-800">
                Daily AirDrop : 0.0033% to claim
              </span>
            )} */}
          </div>
        </div>
      </div>
    </>
  );
}
