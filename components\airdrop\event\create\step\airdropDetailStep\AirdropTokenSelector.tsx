import FetchDataStateSection from '@/components/shared/FetchDataStateSection';
import NumberDisplay from '@/components/shared/NumberDisplay';
import PopUpModal from '@/components/shared/PopUpModal';
import { useDisclosure } from '@/hooks/use-disclosure';
import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';
import { useWallet } from '@solana/wallet-adapter-react';
import clsx from 'clsx';
import Image from 'next/image';

import { IoChevronDownSharp } from 'react-icons/io5';
import { ISolPrice, useGetSolPriceAirdrop } from '@/services/airdrop';

export interface IAirdropToken extends ISolPrice {
  mint: string;
  owner: string;
  tokenAmount: {
    amount: string;
    decimals: number;
    uiAmount: number | null;
    uiAmountString: string;
  };
}

interface Props {
  value: string | undefined;
  onChange: (value: IAirdropToken) => void;
}

export default function AirdropTokenSelector({ value, onChange }: Props) {
  const { publicKey } = useWallet();

  const { data: tokenBalances, isLoading: isLoadingTokenOwner } = useTokenAccountBuyOwnerBalance(
    publicKey!
  );

  const { data: tokenWithInfo, isLoading: isLoadingTokenPrice } = useGetSolPriceAirdrop(
    tokenBalances ? tokenBalances.slice(0, 60).map((item) => item.mint) : []
  );

  const tokenListModal = useDisclosure();

  const onCloseModal = () => {
    tokenListModal.close();
  };

  const token = tokenWithInfo?.find((item) => item.tokenAddress === value);

  return (
    <>
      <div
        className="min-w-24 inline-flex items-center justify-center rounded-full bg-white border border-gray-200 outline-none px-1.5 h-[38px] gap-2 text-gray-800 cursor-pointer font-medium"
        onClick={() => tokenListModal.open()}
      >
        <div className="flex items-center gap-2">
          {token && typeof token.iconUri === 'string' ? (
            <Image
              src={token.iconUri}
              width={24}
              height={24}
              alt={token.name}
              className="size-6 min-w-6 rounded-full overflow-hidden"
            />
          ) : (
            token?.iconUri
          )}
          {token?.symbol}
        </div>
        <div className="ml-auto">
          <IoChevronDownSharp />
        </div>
      </div>
      {tokenListModal.opened && (
        <PopUpModal
          isOpen={tokenListModal.opened}
          onClose={onCloseModal}
          className="!px-0 !py-6 max-std:h-[80dvh] overflow-y-hidden"
        >
          <div className="sm:max-h-[calc(100dvh-128px)] relative w-full overflow-hidden grow flex flex-col gap-3">
            <div className="flex flex-col gap-4 sticky top-0 bg-white px-5">
              <div className="flex gap-2.5 items-center bg-gray-300 rounded-2xl px-4 py-3 h-[60px]">
                <Image
                  src="/icons/search-icon.svg"
                  width={20}
                  height={20}
                  alt="search icon"
                  className="size-5"
                />
                <input
                  className="outline-none grow input-text-gray-600-black bg-transparent max-w-[calc(100%-40px)]"
                  placeholder="Search"
                />
              </div>
            </div>
            <div className="flex flex-col max-h-[calc(100%-120px)] overflow-y-auto">
              <FetchDataStateSection
                isLoading={isLoadingTokenOwner || isLoadingTokenPrice}
                isEmpty={!tokenWithInfo?.length}
              >
                {tokenWithInfo?.map((token: ISolPrice) => {
                  const userTokenBalance = tokenBalances?.find(
                    (balance) => balance.mint === token.tokenAddress
                  );

                  const isShowBalance =
                    userTokenBalance && Number(userTokenBalance.tokenAmount.uiAmountString);

                  return (
                    <div
                      key={token.id}
                      className="flex items-center gap-2.5 hover:bg-black/5 cursor-pointer py-4 px-5"
                      onClick={() => {
                        onChange({ ...token, ...userTokenBalance } as IAirdropToken);
                        tokenListModal.close();
                      }}
                    >
                      <Image
                        src={token.iconUri as string}
                        width={36}
                        height={36}
                        alt={token.name}
                        className="size-9 rounded-full overflow-hidden"
                      />
                      <div
                        className={clsx(
                          'flex flex-col',
                          isShowBalance ? 'max-w-[50%]' : 'max-w-[calc(100%-80px)]'
                        )}
                      >
                        <span className="text-gray-800 text-sm font-medium truncate">
                          {token.name}
                        </span>
                        <span className="text-gray-600 text-xs font-medium">{token.symbol}</span>
                      </div>
                      <div className="flex flex-col ml-auto items-end">
                        <span className="text-gray-800 text-sm font-medium">
                          {isShowBalance ? (
                            <>
                              <NumberDisplay number={userTokenBalance.tokenAmount.uiAmountString} />
                              &nbsp;{token.symbol}
                            </>
                          ) : (
                            '-'
                          )}
                        </span>
                        <span className="text-gray-600 text-xs font-medium">
                          {isShowBalance ? (
                            <>
                              <NumberDisplay
                                number={userTokenBalance.tokenAmount.uiAmountString}
                                isNormalize
                              />
                              &nbsp;{token.symbol}
                            </>
                          ) : (
                            '-'
                          )}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </FetchDataStateSection>
            </div>
          </div>
        </PopUpModal>
      )}
    </>
  );
}
