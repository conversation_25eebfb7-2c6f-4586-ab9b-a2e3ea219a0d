import { useQuery } from '@tanstack/react-query';

export interface ISwapQuoteParams {
  inputMint: string;
  outputMint: string;
  amount: number;
  enabled?: boolean;
}

export interface ISwapQuote {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee: null | unknown;
  priceImpactPct: string;
  routePlan: Array<{
    swapInfo: {
      ammKey: string;
      label: string;
      inputMint: string;
      outputMint: string;
      inAmount: string;
      outAmount: string;
      feeAmount: string;
      feeMint: string;
    };
    percent: number;
  }>;
  contextSlot: number;
  timeTaken: number;
  swapUsdValue: string;
}

export const useSwapQuote = ({
  inputMint,
  outputMint,
  amount,
  enabled = true,
}: ISwapQuoteParams) => {
  return useQuery({
    queryKey: ['swap-quote', inputMint, outputMint, amount],
    queryFn: (): Promise<ISwapQuote> => {
      return fetch(
        'https://api.jup.ag/swap/v1/quote?' +
          new URLSearchParams({
            inputMint,
            outputMint,
            amount: amount.toString(),
          }).toString(),
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.NEXT_PUBLIC_JUPITER_API_KEY || '',
          },
        }
      ).then((res) => res.json());
    },
    enabled: !!inputMint && !!outputMint && !!amount && enabled,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
};
