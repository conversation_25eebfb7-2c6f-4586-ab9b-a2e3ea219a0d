import {
  EAirdropStatus,
  useGetInfiniteCreatorAirdropList,
  useGetInfiniteCreatorAirdropTotalByToken,
} from '@/services/airdrop';
import AirDropItem from './AirDropItem';
import Button, { BUTTON_SIZE } from '../shared/Button';
import clsx from 'clsx';
import { getBtnClass, getStatusText, MIN_SOL_CLAIMABLE } from './common';
import FetchDataStatementWrapper from '../shared/FetchDataStatementWrapper';
import BigNumber from 'bignumber.js';
import { formatNumber } from '@/utils/common';
import React, { useMemo, useState } from 'react';
import { useBondingCurveSdkContext } from '@/contexts/sdk.context';
import { PublicKey } from '@solana/web3.js';
import { toast } from 'react-toastify';
import { formatDate } from 'date-fns';
import LoadMoreButton from '../shared/LoadMoreButton';
import { useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '@/services/query-keys';
import { useGetMyCreatorAirdrop } from './hooks/use-get-my-creator-airdrop';

interface Props {
  currentFilter: EAirdropStatus;
  onUnClaimableClick: () => void;
  claimedCreatorAirdrop: string[];
  setClaimedCreatorAirdrop: React.Dispatch<React.SetStateAction<string[]>>;
}

export default function CreatorAirdropTab({
  currentFilter,
  onUnClaimableClick,
  claimedCreatorAirdrop,
  setClaimedCreatorAirdrop,
}: Props) {
  const { sdk, publicKey, connection } = useBondingCurveSdkContext();

  const queryClient = useQueryClient();

  //Fetch data queries
  const {
    data: myCreatorAirdrops,
    isLoading: isLoadingMyCreatorAirdrops,
    allMyCoins,
  } = useGetMyCreatorAirdrop(currentFilter === EAirdropStatus.CLAIM);
  const {
    data: myClaimedCreatorAirdrops,
    isLoading: isLoadingClaimedCreatorAirdrops,
    hasNextPage: hasNextClaimedCreatorAirdropPage,
    fetchNextPage: fetchNextClaimedCreatorAirdropPage,
    isFetching: isFetchingClaimedCreatorAirdrops,
    isRefetching: isRefetchingClaimedCreatorAirdrops,
  } = useGetInfiniteCreatorAirdropList(currentFilter === EAirdropStatus.CHECK);
  const {
    data: totalCreatorAirdropByToken,
    isLoading: isLoadingTotalCreatorAirdropByToken,
    hasNextPage: hasNextPageTotalCreatorAirdropByToken,
    fetchNextPage: fetchNextPageTotalCreatorAirdropByToken,
    isFetching: isFetchingTotalCreatorAirdropByToken,
    isRefetching: isRefetchingTotalCreatorAirdropByToken,
  } = useGetInfiniteCreatorAirdropTotalByToken(currentFilter === EAirdropStatus.TOTAL);

  const [updatingIds, setUpdatingIds] = useState<Array<number | string>>([]);

  const handleWithdrawCreatorAirdrop = async (mint: string) => {
    try {
      setUpdatingIds((prev) => [...prev, 'creator' + mint]);
      const func = await sdk?.withdrawCreatorFeeTx(new PublicKey(mint), publicKey!);

      if (!func) {
        toast.error('Failed to create transaction function');
        return;
      }
      const latestBlockhash = await connection.getLatestBlockhash();
      const transaction = await func.transaction();
      transaction.recentBlockhash = latestBlockhash.blockhash;
      transaction.feePayer = publicKey!;

      const { signature } = await window.solana.signAndSendTransaction(transaction as any);

      await connection.getSignatureStatus(signature);

      toast.success('Successfully claimed!');

      setClaimedCreatorAirdrop((prev) => [...prev, mint]);
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
        });
      }, 1000);
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
        });
      }, 2500);
    } catch (error) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message
          : 'Error withdrawing creator airdrop';
      toast.error(errorMessage || 'Claim failed!');
    } finally {
      setUpdatingIds((prev) => prev.filter((item) => item !== 'creator' + mint));
      setTimeout(
        () =>
          queryClient.invalidateQueries({
            queryKey: [EQueryKeys.CREATOR_AIRDROP],
          }),
        2000
      );
    }
  };

  //Flat pagination data
  const flattenClaimedCreatorAirdrops = useMemo(() => {
    if (!myClaimedCreatorAirdrops) return [];
    return myClaimedCreatorAirdrops.pages.flatMap((page) => page.data);
  }, [myClaimedCreatorAirdrops]);

  const filteredCreatorAirdrops = useMemo(() => {
    if (!myCreatorAirdrops) return [];
    return myCreatorAirdrops.filter(
      (airdrop) => !claimedCreatorAirdrop.some((address) => airdrop.tokenAddress === address)
    );
  }, [myCreatorAirdrops, claimedCreatorAirdrop]);

  const flattenTotalCreatorAirdropByToken = useMemo(() => {
    if (!totalCreatorAirdropByToken) return [];
    return totalCreatorAirdropByToken.pages.flatMap((page) => page.data);
  }, [totalCreatorAirdropByToken]);

  //Fetching state
  const isLoading = useMemo(() => {
    switch (currentFilter) {
      case EAirdropStatus.CLAIM:
        return isLoadingMyCreatorAirdrops;
      case EAirdropStatus.CHECK:
        return isLoadingClaimedCreatorAirdrops || isRefetchingClaimedCreatorAirdrops;
      case EAirdropStatus.TOTAL:
        return isLoadingTotalCreatorAirdropByToken || isRefetchingTotalCreatorAirdropByToken;
    }
  }, [
    currentFilter,
    isLoadingMyCreatorAirdrops,
    isLoadingClaimedCreatorAirdrops,
    isRefetchingClaimedCreatorAirdrops,
    isLoadingTotalCreatorAirdropByToken,
    isRefetchingTotalCreatorAirdropByToken,
  ]);

  const isEmptyData = useMemo(() => {
    switch (currentFilter) {
      case EAirdropStatus.CLAIM:
        return !filteredCreatorAirdrops.length;
      case EAirdropStatus.CHECK:
        return !flattenClaimedCreatorAirdrops.length;
      case EAirdropStatus.TOTAL:
        return !flattenTotalCreatorAirdropByToken;
    }
  }, [
    currentFilter,
    filteredCreatorAirdrops,
    flattenClaimedCreatorAirdrops,
    flattenTotalCreatorAirdropByToken,
  ]);

  return (
    <>
      <FetchDataStatementWrapper
        isLoading={!!isLoading}
        isEmptyData={isEmptyData}
        customNotDataComponent={
          <AirDropItem
            title="No items found"
            isSoon
            actionButton={
              <Button
                size={BUTTON_SIZE.SMALL}
                className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.SOON))}
                disabled
              >
                {getStatusText(EAirdropStatus.SOON)}
              </Button>
            }
          />
        }
      >
        <div className="w-full flex flex-col gap-3">
          {[EAirdropStatus.CLAIM].includes(currentFilter) &&
            filteredCreatorAirdrops.map((airdrop) => {
              const isUnClaimable = new BigNumber(airdrop.balance).lte(
                new BigNumber(MIN_SOL_CLAIMABLE)
              );
              return (
                <AirDropItem
                  key={'creator-' + airdrop.tokenAddress}
                  progress={100}
                  icon={airdrop.iconUri}
                  title={`$${airdrop.symbol}`}
                  subTitle={`${formatNumber(airdrop.balance)} SOL`}
                  actionButton={
                    <div className="relative">
                      <Button
                        size={BUTTON_SIZE.SMALL}
                        className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.CLAIM))}
                        onClick={() => handleWithdrawCreatorAirdrop(airdrop.tokenAddress)}
                        isLoading={updatingIds.includes('creator' + airdrop.tokenAddress)}
                        disabled={isUnClaimable}
                      >
                        {getStatusText(EAirdropStatus.CLAIM)}
                      </Button>
                      {isUnClaimable && (
                        <div
                          className="absolute w-full h-full top-0 left-0"
                          onClick={onUnClaimableClick}
                        ></div>
                      )}
                    </div>
                  }
                />
              );
            })}
          {[EAirdropStatus.CHECK].includes(currentFilter) &&
            flattenClaimedCreatorAirdrops?.map((airdrop) => {
              const coin = allMyCoins?.find((item) => item.tokenAddress === airdrop.token_address);
              return (
                <AirDropItem
                  key={airdrop.id}
                  progress={0}
                  icon="/icons/chain-sol.svg"
                  title={`$${coin?.symbol || '???'}`}
                  subTitle={formatDate(airdrop.created_at, 'dd MMM yyyy')}
                  actionButton={
                    <span className="text-gray-900 font-medium">{`+${formatNumber(airdrop.sol_amount, 4)} SOL`}</span>
                  }
                  isHistory
                />
              );
            })}
          {[EAirdropStatus.TOTAL].includes(currentFilter) &&
            flattenTotalCreatorAirdropByToken?.map((airdrop) => {
              const coin = allMyCoins?.find((item) => item.tokenAddress === airdrop.token_address);
              return (
                <AirDropItem
                  key={airdrop.token_address}
                  progress={100}
                  icon={coin?.iconUri || '/icons/chain-sol.svg'}
                  title={`$${coin?.symbol || '???'}`}
                  actionButton={
                    <span className="text-sm input-text-gray-600-black  font-medium">{`${formatNumber(airdrop.total_sol, 4)} SOL`}</span>
                  }
                  wrapperClassName="!px-5"
                />
              );
            })}
          {currentFilter === EAirdropStatus.CHECK && (
            <div className="flex justify-center w-full">
              <LoadMoreButton
                hasNextPage={!!hasNextClaimedCreatorAirdropPage}
                isFetching={isFetchingClaimedCreatorAirdrops}
                fetchNextPage={fetchNextClaimedCreatorAirdropPage}
              >
                View More
              </LoadMoreButton>
            </div>
          )}
          {currentFilter === EAirdropStatus.TOTAL && (
            <div className="flex justify-center w-full">
              <LoadMoreButton
                hasNextPage={!!hasNextPageTotalCreatorAirdropByToken}
                isFetching={isFetchingTotalCreatorAirdropByToken}
                fetchNextPage={fetchNextPageTotalCreatorAirdropByToken}
              >
                View More
              </LoadMoreButton>
            </div>
          )}
        </div>
      </FetchDataStatementWrapper>
    </>
  );
}
