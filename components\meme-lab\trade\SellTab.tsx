import { useCoinPageContext } from '@/contexts/meme.context';
import { useSolUsdPrice } from '@/services/meme-launchpad';
import { useWallet } from '@solana/wallet-adapter-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import React, { Dispatch, SetStateAction, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { getMinSolCost, TradeFormData, TRADING_SELL_FEE } from './common';
import BigNumber from 'bignumber.js';
import { convertDecToMist, convertMistToDec } from '@/utils/helper';
import { PublicKey } from '@solana/web3.js';
import { BN } from '@coral-xyz/anchor';
import { floor, round } from 'lodash';
import { toast } from 'react-toastify';
import { EQueryKeys } from '@/services/query-keys';
import { formatNumber, formatNumberWithCommas, parsedChainErrorMessage } from '@/utils/common';
import clsx from 'clsx';
import Image from 'next/image';
import { SlippageSelector } from './SlippageSelector';
import { useDebounceValue } from 'usehooks-ts';
import { useBondingCurveSdkContext } from '@/contexts/sdk.context';
import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';

const percentageOfMoney = [
  { label: '10%', value: '0.1' },
  { label: '50%', value: '0.5' },
  { label: '80%', value: '0.8' },
  { label: 'Max', value: '1' },
];

export default function SellTab({
  formRef,
  setIsLoading,
  closePopUp,
}: {
  formRef: React.RefObject<HTMLFormElement>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  closePopUp?: () => void;
}) {
  const { publicKey, signTransaction } = useWallet();
  const { coin } = useParams();
  const queryClient = useQueryClient();

  const { data: tokenBalances } = useTokenAccountBuyOwnerBalance(publicKey!);
  const { sdk, connection } = useBondingCurveSdkContext();
  const { coinAddress, isCurveComplete, coin: coinInfo, coinSymbol } = useCoinPageContext();
  const { data: priceUsdData } = useSolUsdPrice();

  const [debouncedAmount, setDebouncedAmount] = useDebounceValue('', 500);

  const { priceUsd } = priceUsdData || {};

  const balance = useMemo(() => {
    if (!tokenBalances || !coin) return 0;

    const tokenBalance = tokenBalances.find((item) => item.mint === coin);

    return tokenBalance ? Number(tokenBalance.tokenAmount.uiAmountString) : 0;
  }, [coin, tokenBalances]);

  const maxAmount = balance;

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
    watch,
    getValues,
  } = useForm<TradeFormData>({
    defaultValues: {
      amount: '',
      slippage: '0.5',
    },
    mode: 'onChange',
  });

  const amount = watch('amount');
  const slippage = watch('slippage');

  const { data: estimatedAmountOut } = useQuery({
    queryKey: ['est-sell-amount', debouncedAmount],
    queryFn: async () => {
      if (!sdk) return;

      if (!debouncedAmount) return '0';

      const res = await sdk.getSellPrice(
        new PublicKey(coinAddress),
        new BN(convertDecToMist(debouncedAmount, 6))
      );
      if (Number(res) > 0) return res.toString();
    },
    enabled: !!sdk && !!debouncedAmount,
  });

  const bonusPoint = useMemo(() => {
    if (!priceUsd || !estimatedAmountOut) return 0;
    const bonus = new BigNumber(convertMistToDec(estimatedAmountOut)).multipliedBy(priceUsd);
    return bonus;
  }, [priceUsd, estimatedAmountOut]);

  // Expose setValue for parent component
  useEffect(() => {
    if (formRef.current) {
      (formRef.current as any).setValue = setValue;
      (formRef.current as any).getValues = getValues;
    }
  }, [formRef, setValue, getValues]);

  useEffect(() => {
    setDebouncedAmount(amount);
  }, [amount, setDebouncedAmount]);

  // useEffect(() => {
  //   setEstimateAmountOut('');

  //   if (!amount || !sdk || isCurveComplete) {
  //     return;
  //   }

  //   const getEstimateAmountOut = async () => {
  //     const res = await sdk.getSellPrice(
  //       new PublicKey(coinAddress),
  //       new BN(convertDecToMist(amount, 6))
  //     );
  //     if (Number(res) > 0) setEstimateAmountOut(res.toString());
  //   };
  //   getEstimateAmountOut();
  // }, [amount, sdk]);

  const onSubmit = async (data: TradeFormData) => {
    if (!sdk) {
      toast.error('SDK not initialized');
      return;
    }

    if (isCurveComplete) {
      toast.error('Curve is complete, trading is not available');
      return;
    }

    const { amount } = data;

    if (Number(amount) <= 0) {
      toast.error('Amount must be greater than 0');
      return;
    }

    const comparedNumber = new BigNumber(amount).comparedTo(new BigNumber(balance || 0));

    if (comparedNumber && comparedNumber > 0) {
      toast.error('Insufficient balance');
      return;
    }

    setIsLoading(true);

    try {
      const latestBlockhash = await connection.getLatestBlockhash();

      const minSellCost = getMinSolCost(estimatedAmountOut, Number(slippage));

      const func = await sdk.sellTx(
        new PublicKey(coinAddress),
        new BN(floor(Number(convertDecToMist(amount, 6)), 6)),
        new BN(round(Number(minSellCost)))
      );
      const transaction = await func.transaction();
      transaction.recentBlockhash = latestBlockhash.blockhash;
      transaction.feePayer = publicKey!;

      if (!signTransaction || !transaction) {
        toast.error('Failed to create transaction');
        return;
      }

      const { signature } = await window.solana.signAndSendTransaction(transaction as any);

      await connection.getSignatureStatus(signature);

      toast.success('Sell successful!');
      reset();
      if (closePopUp) {
        closePopUp();
      }
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.TOKEN_OWNER_BALANCE],
      });
    } catch (error: any) {
      const { errorMessage } = parsedChainErrorMessage(error.message || '');
      toast.error(String(errorMessage) || 'Sell failed!');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-6">
      <div className="flex flex-col gap-[10px]">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <label className="text-[#666666] text-base font-normal ml-2">Quantity</label>
          </div>
          <span className="text-[#666666] text-xs font-normal">
            Balance {formatNumber(balance, 2)}
          </span>
        </div>
        <Controller
          name="amount"
          control={control}
          rules={{
            required: 'Amount is required',
            validate: (value) =>
              (!isNaN(parseFloat(value)) && parseFloat(value) > 0) || 'Please enter a valid amount',
            max: {
              value: maxAmount,
              message: maxAmount
                ? `Maximum amount is ${maxAmount} ${coinSymbol}`
                : "You don't own this token.",
            },
          }}
          render={({ field, fieldState: { error } }) => (
            <div className="flex flex-col gap-1">
              <div
                className={clsx(
                  'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                  error ? 'border-red-500' : 'border-gray-200'
                )}
              >
                <input
                  {...field}
                  type="number"
                  inputMode="decimal"
                  onChange={(e) => {
                    // Only allow numbers and one decimal point
                    const value = e.target.value.replace(/[^\d.]/g, '');
                    const parts = value.split('.');
                    if (parts.length > 2) {
                      return;
                    }
                    field.onChange(value);
                  }}
                  className="bg-transparent max-w-[calc(100%-100px)] focus:outline-none"
                />
                <div className="flex items-center w-full max-w-[100px] bg-white border border-gray-200 rounded-full gap-2 py-2 px-2.5">
                  <Image
                    className="size-6 rounded-full aspect-square object-cover"
                    src={coinInfo.iconUri}
                    alt="Token icon"
                    width={24}
                    height={24}
                  />
                  <span className="font-medium text-gray-800 truncate max-w-[calc(100%-32px)]">
                    {coinSymbol}
                  </span>
                </div>
              </div>
              {error && <p className="text-red-500 text-xs mt-1">{error.message}</p>}
            </div>
          )}
        />
        <div className="flex gap-3">
          {percentageOfMoney.map((option) => (
            <button
              type="button"
              key={option.value}
              onClick={() => {
                setValue('amount', floor(maxAmount * parseFloat(option.value), 6).toString(), {
                  shouldValidate: true,
                });
              }}
              className="flex-1 w-full h-[38px] py-3 text-center rounded-full text-sm leading-none transition-all border border-gray-200 bg-white text-gray-800 hover:bg-primary-500/10 hover:text-primary-500 hover:border-primary-500/30"
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
      <div className="mt-5">
        <div className="flex justify-between items-center py-4 text-[#666666] text-base font-normal">
          <p>Point</p>
          <span className="rounded-full bg-primary-500/10 text-primary-500 text-xs font-medium py-1.5 px-[14px]">
            +{formatNumberWithCommas(floor(Number(bonusPoint)))}
            &nbsp;PT
          </span>
        </div>
        <div className="flex justify-between items-center py-4">
          <p className="text-[#666666] text-base font-normal">Slippage</p>
          <div className="">
            <Controller
              name="slippage"
              control={control}
              rules={{ required: 'Slippage is required' }}
              render={({ field }) => (
                <SlippageSelector
                  value={field.value}
                  onChange={field.onChange}
                  error={!!errors.slippage}
                />
              )}
            />
          </div>
        </div>
        <div className="flex justify-between items-center py-4 text-gray-800 text-base font-normal">
          <p>Fee</p>
          <p>{amount ? TRADING_SELL_FEE : 0} SOL</p>
        </div>
      </div>
    </form>
  );
}
