import React from 'react';
import SvgCircle from '../shared/SvgCircle';
import Image from 'next/image';

import QuestionMarkIcon from '@/public/icons/question-mark-icon.svg';
import DepositIcon from '@/public/icons/deposit-icon.svg';
import { formatDate } from 'date-fns';
import clsx from 'clsx';

interface AirdropItemProps {
  icon?: string;
  progress?: number;
  title?: string;
  subTitle?: string;
  date?: string | Date;
  actionButton: React.ReactNode;
  onRedirect?: () => void;
  claimable?: boolean;
  isSoon?: boolean;
  isHistory?: boolean;
  wrapperClassName?: string;
}

export default function AirDropItem({
  icon,
  progress = 0,
  title,
  subTitle,
  date,
  actionButton,
  onRedirect,
  claimable = true,
  isSoon = false,
  isHistory = false,
  wrapperClassName,
}: AirdropItemProps) {
  return (
    <div
      className={clsx(
        'py-3 px-[14px] bg-white rounded-std flex items-center justify-between gap-[18px]',
        wrapperClassName
      )}
    >
      {!isHistory ? (
        <div className="relative">
          <SvgCircle progress={progress} />
          <div
            className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center cursor-pointer"
            onClick={onRedirect}
          >
            {icon ? (
              <Image
                src={icon}
                alt={'Airdrop Logo'}
                width={48}
                height={48}
                className="rounded-full size-12 aspect-square object-cover"
              />
            ) : (
              <div className="rounded-full size-12 bg-gradient-to-b from-[#ffab00] to-[#ff4b00] flex justify-center items-center">
                <QuestionMarkIcon />
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className={'rounded-full size-12 bg-[#EE811A1A] flex items-center justify-center'}>
          <DepositIcon width={24} height={24} />
        </div>
      )}
      <div className="grow flex items-center justify-between">
        <div className="flex flex-col gap-1.5">
          <span className="text-gray-900 font-medium cursor-pointer" onClick={onRedirect}>
            {title || '???'}
          </span>
          {!claimable && !isSoon && (
            <span className="text-sm input-text-gray-600-black font-medium">Not eligible</span>
          )}
          {(subTitle || date) && (
            <div className="flex items-center gap-1.5 input-text-gray-600-black font-medium">
              {subTitle && <span className="text-sm leading-4">{subTitle}</span>}
              {date && <span className="text-xs">{formatDate(date, 'dd MMM yyyy')}</span>}
            </div>
          )}
        </div>
        {actionButton}
      </div>
    </div>
  );
}
