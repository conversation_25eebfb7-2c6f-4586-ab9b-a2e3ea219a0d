import { useQuery } from '@tanstack/react-query';
import { ISwapQuote } from './use-swap-quote';
import { PublicKey } from '@solana/web3.js';

interface ISwapTransactionParams {
  quoteResponse?: ISwapQuote;
  userPublicKey: string | PublicKey;
  enabled?: boolean;
}

export const QUOTE_ERROR_CODE = {
  COULD_NOT_FIND_ANY_ROUTE: 'COULD_NOT_FIND_ANY_ROUTE',
};

export default function useBuildSwapTransaction({ enabled, ...params }: ISwapTransactionParams) {
  const query = useQuery({
    queryKey: ['build-swap-transaction', params.quoteResponse, params.userPublicKey],
    queryFn: async () => {
      return await fetch('https://api.jup.ag/swap/v1/swap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_JUPITER_API_KEY || '',
        },
        body: JSON.stringify({
          ...params,

          // ADDITIONAL PARAMETERS TO OPTIMIZE FOR TRANSACTION LANDING
          // See next guide to optimize for transaction landing
          dynamicComputeUnitLimit: true,
          dynamicSlippage: true,
          prioritizationFeeLamports: {
            priorityLevelWithMaxLamports: {
              maxLamports: 1000000,
              priorityLevel: 'veryHigh',
            },
          },
        }),
      }).then((res) =>
        res.json().then((data) => {
          return data;
        })
      );
    },
    enabled: !!params.userPublicKey && !!params.quoteResponse && enabled,
    refetchOnWindowFocus: false,
  });
  return query;
}
