import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import {
  IAirdropEventConfig,
  IAirdropEventHoldersParams,
  IAirdropHoldersParams,
} from './airdrop.types';
import { AirdropService } from './airdrop.service';

export const useGetAirdropHolders = (params: IAirdropHoldersParams) => {
  return useInfiniteQuery({
    queryKey: ['getAirdropHolders', params],
    queryFn: ({ pageParam = 1 }) =>
      AirdropService.getAirdropHolders({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.nextPage;
    },
    initialPageParam: 1,
  });
};

export const usePostAirdropEventHolders = () => {
  return useMutation({
    mutationFn: (params: IAirdropEventHoldersParams) =>
      AirdropService.postAirdropEventHolders(params),
  });
};

export const usePostAirdropEventConfig = () => {
  return useMutation({
    mutationFn: (payload: IAirdropEventConfig) => AirdropService.postAirdropEventConfig(payload),
  });
};
