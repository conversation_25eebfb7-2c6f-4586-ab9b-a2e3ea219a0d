import Image from 'next/image';
import { IAirdropSpInfo } from '@/services/airdropSp/airdrop.types';
import { useMemo } from 'react';
import Smapocke from '@/public/icons/smapocke_v2.png';
import { useGetUserRank } from '@/services/auth';
import { routePaths } from '@/constants/common';
import { useAuth } from '@/hooks/use-auth';
import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';
import { useGetSummaryPoint } from '@/services/airdrop';
import {
  formatNumberWithCommas,
  formatNumber,
  formatNumberWithSubscriptZeros,
} from '@/utils/common';
import { PublicKey } from '@solana/web3.js';
import clsx from 'clsx';
import { round } from 'lodash';
import Link from 'next/link';
import { IoIosArrowForward } from 'react-icons/io';

type Props = {
  airdropSpInfo?: IAirdropSpInfo;
};
const AirdropTokens = ({ airdropSpInfo }: Props) => {
  const { isAuthenticated, user } = useAuth();
  const { data: userRanking } = useGetUserRank(isAuthenticated);
  const { data: summaryPoint, isLoading: isLoadingSummaryPoint } =
    useGetSummaryPoint(isAuthenticated);
  const { data: tokenBalances, isLoading: isLoadingBalance } = useTokenAccountBuyOwnerBalance(
    user && user.solana_address ? new PublicKey(user?.solana_address) : undefined,
    'mainnet'
  );
  const totalAirdropPercent = useMemo(() => {
    if (!airdropSpInfo) return 0;
    return (
      airdropSpInfo?.pkm.sp_percent * 100 +
      airdropSpInfo?.holy.sp_percent * 100 +
      airdropSpInfo?.ranking.sp_percent * 100
    );
  }, [airdropSpInfo]);
  const ListAirdrop = useMemo(
    () => [
      {
        tokenAddress: '6LH9NDBYUf7thDx8sMZppZpxAsdXtsQ9VN37gKMpZRSp',
        icon: 'https://smartpocke.sotatek.works/_next/image?url=https%3A%2F%2Fstatic.dev-smartpocke.sotatek.works%2Fd8b106b8-3ea9-4bc0-96cf-d910fc9b519c.webp&w=256&q=75',
        name: 'Pockemy',
        symbol: '$PKM',
        color: '#FFD283',
        currency: 'PKM',
        percent: (airdropSpInfo?.pkm.sp_percent || 0) * 100,
        totalPercent: (airdropSpInfo?.pkm.config_percent || 0) * 100,
      },
      {
        tokenAddress: 'BSXFKLVsyrYckSF4SGXu9DRfMb3Fos7tRF9zMXgRCUsP',
        icon: 'https://smartpocke.sotatek.works/_next/image?url=https%3A%2F%2Fstatic.smapocke-marketplace.app%2F3943258e-cd81-4a42-91dd-fd617663a071.png&w=256&q=75',
        name: 'Holy Coin',
        symbol: '$HOLY',
        color: '#7AD2FF',
        currency: 'HOLY',
        percent: (airdropSpInfo?.holy.sp_percent || 0) * 100,
        totalPercent: (airdropSpInfo?.holy.config_percent || 0) * 100,
      },
      {
        icon: Smapocke.src,
        name: 'SP Point',
        symbol: 'Point',
        color: '#FF9F75',
        currency: 'PT',
        percent: (airdropSpInfo?.ranking.sp_percent || 0) * 100,
        totalPercent: (airdropSpInfo?.ranking.config_percent || 0) * 100,
      },
    ],
    [airdropSpInfo]
  );
  return (
    <>
      <div className="flex justify-between items-center text-primary-500 bg-primary-500/10 rounded-2xl px-5 py-2">
        <span className="text-sm font-medium">Your Airdrop Allocation</span>
        <div className="text-xl font-medium">
          <span className="text-sm">
            {formatNumberWithSubscriptZeros(totalAirdropPercent.toString())}%
          </span>
          <span className="text-xs"> / 100%</span>
        </div>
      </div>
      <div className="flex flex-col">
        {ListAirdrop.map((item, index) => {
          const isPoint = item.symbol === 'Point';
          const currentTokenBalance = tokenBalances?.find(
            (token) => token.mint === item.tokenAddress
          );
          const balance = isPoint
            ? summaryPoint
              ? formatNumberWithCommas(round(summaryPoint?.point || 0))
              : 0
            : currentTokenBalance
              ? formatNumber(currentTokenBalance?.tokenAmount.uiAmountString || 0, 2)
              : 0;
          return (
            <Link
              className="flex gap-[18px] py-3.5"
              key={index}
              href={isPoint ? `#` : `${routePaths.swap}?&inputMint=${item.tokenAddress}`}
            >
              <Image
                src={item.icon}
                alt={'Airdrop Logo'}
                width={48}
                height={48}
                className="rounded-full size-12 object-cover"
              />
              <div className="w-full flex flex-col gap-2.5">
                <div className="flex items-center gap-[18px]">
                  <div className="flex flex-col gap-2">
                    <div className="font-medium text-gray-900 leading-none">{item.symbol}</div>
                    <span className="text-sm text-gray-600 leading-none">{item.name}</span>
                  </div>
                  <div className="grow flex gap-2.5 items-center justify-end">
                    <div className="flex flex-col justify-center gap-2 text-end">
                      <span
                        className={clsx('font-medium text-gray-900 leading-none', {
                          'animate-pulse': isLoadingBalance || isLoadingSummaryPoint,
                        })}
                      >
                        {balance ? balance : '0'} {item.currency}
                      </span>
                      <span className="text-sm text-gray-600 leading-none">
                        {isPoint &&
                          `${userRanking?.rankName ? userRanking?.rankName : 'No'} Rank : `}
                        {formatNumberWithSubscriptZeros(item.percent.toString())}% /{' '}
                        {item.totalPercent}%
                      </span>
                    </div>
                    {!isPoint ? (
                      <IoIosArrowForward className="text-gray-600 size-4" />
                    ) : (
                      <span className="size-4 invisible"></span>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </>
  );
};

export default AirdropTokens;
