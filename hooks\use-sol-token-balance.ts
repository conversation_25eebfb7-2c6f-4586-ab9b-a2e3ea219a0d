import {
  isMainnet,
  quickNodeRpc,
  SOLANA_TOKEN_ADDRESS,
  SOLANA_TOKEN_PROGRAM,
} from '@/lib/web3/constants';
import { EQueryKeys } from '@/services/query-keys';
import { convertMistToDec } from '@/utils/helper';
import { Connection, PublicKey } from '@solana/web3.js';
import { useQuery } from '@tanstack/react-query';

export default function useSolTokenBalance(
  userPublicKey?: PublicKey,
  network?: 'mainnet' | 'devnet'
) {
  return useQuery({
    queryKey: [EQueryKeys.SOL_TOKEN_BALANCE, userPublicKey],
    queryFn: async () => {
      const connection = new Connection(
        (isMainnet || network === 'mainnet') && quickNodeRpc.mainnet
          ? quickNodeRpc.mainnet
          : quickNodeRpc.devnet,
        'confirmed'
      );

      let tokenAdd = new PublicKey(SOLANA_TOKEN_PROGRAM!);

      const [nativeBalance, tokenBalance] = await Promise.all([
        connection.getBalance(new PublicKey(userPublicKey!)),
        connection.getParsedTokenAccountsByOwner(new PublicKey(userPublicKey!), {
          programId: tokenAdd,
        }),
      ]);
      const parsedTokenBalance = tokenBalance.value.map((item) => ({
        mint: item.account.data.parsed.info.mint,
        owner: item.account.data.parsed.info.owner,
        tokenAmount: item.account.data.parsed.info.tokenAmount,
      }));
      return [
        {
          tokenAmount: {
            amount: nativeBalance.toString(),
            uiAmount: convertMistToDec(nativeBalance),
            uiAmountString: convertMistToDec(nativeBalance).toString(),
            decimals: 9,
          },
          mint: SOLANA_TOKEN_ADDRESS,
          owner: userPublicKey!.toBase58(),
        },
        ...parsedTokenBalance,
      ];
    },
    refetchOnWindowFocus: false,
    staleTime: 60 * 60 * 1000, // 1 hour
    enabled: !!userPublicKey,
  });
}
