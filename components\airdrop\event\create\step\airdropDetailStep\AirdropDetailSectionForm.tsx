import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';
import DatabaseIcon from '@/public/icons/database.svg';
import { useWallet } from '@solana/wallet-adapter-react';
import { floor } from 'lodash';
import { useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { EventAirdropSchemaFormType } from '../../schema';
import clsx from 'clsx';
import { formatNumber } from '@/utils/common';
import AirdropTokenSelector from './AirdropTokenSelector';

const percentageOfBalance = [
  { label: '10%', value: '0.1' },
  { label: '50%', value: '0.5' },
  { label: '80%', value: '0.8' },
  { label: 'Max', value: '1' },
];

export default function AirdropDetailSectionForm() {
  const { publicKey } = useWallet();

  const { data: tokenBalances } = useTokenAccountBuyOwnerBalance(publicKey!);

  const { watch, control, setValue } = useFormContext<EventAirdropSchemaFormType>();

  const amount = useMemo(() => {
    if (!watch('airdropTokenAddress')) return 0;
    const token = tokenBalances?.find((item) => item.mint === watch('airdropTokenAddress'));
    return token ? Number(token.tokenAmount.uiAmountString) : 0;
  }, [tokenBalances, watch('airdropTokenAddress')]);

  return (
    <div className="w-full rounded-std bg-white py-6 px-5 flex flex-col gap-6">
      <div className="w-full flex items-center gap-[18px]">
        <div className="rounded-full bg-primary-500/10 flex items-center justify-center size-12">
          <DatabaseIcon />
        </div>
        <span className="text-gray-800 font-medium text-xl">AirDrop Details</span>
      </div>
      <div className="flex flex-col gap-[10px]">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <label className="text-[#666666] text-base font-normal ml-2">AirDrop Quantity</label>
          </div>
          <span className="text-[#666666] text-xs font-normal">
            Balance {amount ? formatNumber(amount, 2) : 0}
          </span>
        </div>
        <Controller
          name="airdropQuantity"
          control={control}
          rules={{
            required: 'Amount is required',
            validate: (value) =>
              (!isNaN(parseFloat(value)) && parseFloat(value) > 0) || 'Please enter a valid amount',
            max: {
              value: amount,
              message: amount ? `Maximum amount is ${amount}` : "You don't own this token.",
            },
          }}
          render={({ field, fieldState: { error } }) => (
            <div className="flex flex-col gap-1">
              <div
                className={clsx(
                  'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
                  error ? 'border-red-500' : 'border-gray-300'
                )}
              >
                <input
                  {...field}
                  type="number"
                  inputMode="decimal"
                  onChange={(e) => {
                    // Only allow numbers and one decimal point
                    const value = e.target.value.replace(/[^\d.]/g, '');
                    const parts = value.split('.');
                    if (parts.length > 2) {
                      return;
                    }
                    field.onChange(value);
                  }}
                  className="bg-transparent max-w-[calc(100%-100px)] focus:outline-none"
                />
                <AirdropTokenSelector
                  value={watch('airdropTokenAddress')}
                  onChange={(val) => setValue('airdropTokenAddress', val.tokenAddress)}
                />
              </div>
              {error && <p className="text-red-500 text-xs mt-1">{error.message}</p>}
            </div>
          )}
        />
        <div className="flex gap-3">
          {percentageOfBalance.map((option) => (
            <button
              type="button"
              key={option.value}
              onClick={() => {
                setValue(
                  'airdropQuantity',
                  floor(amount * parseFloat(option.value), 6).toString(),
                  {
                    shouldValidate: true,
                  }
                );
              }}
              className="flex-1 w-full h-[38px] py-3 text-center rounded-full text-sm leading-none transition-all border border-gray-200 bg-white text-gray-800 hover:bg-primary-500/10 hover:text-primary-500 hover:border-primary-500/30"
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
